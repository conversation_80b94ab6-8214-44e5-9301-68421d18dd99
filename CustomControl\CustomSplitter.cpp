#include "CustomSplitter.h"

//===== CustomSplitterHandle 实现 =====

CustomSplitterHandle::CustomSplitterHandle(Qt::Orientation orientation, QSplitter *parent) : QSplitterHandle(orientation, parent)
{
    // 设置鼠标悬停时的光标样式
    if (orientation == Qt::Horizontal)
        setCursor(Qt::SplitHCursor);
    else
        setCursor(Qt::SplitVCursor);
}

void CustomSplitterHandle::setColor(const QColor &color)
{
    m_color = color;
    update();
}

void CustomSplitterHandle::setLineWidth(int width)
{
    m_lineWidth = width;
    update();
}

void CustomSplitterHandle::setImage(const QPixmap &pixmap)
{
    m_pixmap = pixmap;
    update();
}

void CustomSplitterHandle::setDashed(bool dashed)
{
    m_dashed = dashed;
    update();
}

void CustomSplitterHandle::setDashPattern(const QVector<qreal> &pattern)
{
    m_dashPattern = pattern;
    update();
}

void CustomSplitterHandle::paintEvent(QPaintEvent *event)
{
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing, true);

    QRect rect = event->rect();

    // 如果设置了背景图片并且有效，则绘制图片
    if (!m_pixmap.isNull()) {
        painter.drawPixmap(rect, m_pixmap);
        return;
    }

    // 绘制颜色填充或虚线
    QPen pen(m_color, m_lineWidth);

    if (m_dashed) {
        pen.setStyle(Qt::CustomDashLine);
        pen.setDashPattern(m_dashPattern);
    }

    painter.setPen(pen);

    // 根据方向绘制分隔线
    if (orientation() == Qt::Horizontal) {
        // 水平分隔线，绘制垂直线条
        int center = rect.center().x();
        painter.drawLine(center, rect.top(), center, rect.bottom());
    } else {
        // 垂直分隔线，绘制水平线条
        int center = rect.center().y();
        painter.drawLine(rect.left(), center, rect.right(), center);
    }
}

//===== CustomSplitter 实现 =====

CustomSplitter::CustomSplitter(QWidget *parent) : QSplitter(parent)
{
    setHandleDashed(true);
}

CustomSplitter::CustomSplitter(Qt::Orientation orientation, QWidget *parent) : QSplitter(orientation, parent)
{
}

void CustomSplitter::setHandleColor(const QColor &color)
{
    m_handleColor = color;

    // 更新所有现有的分隔线句柄
    for (int i = 0; i < count() - 1; ++i) {
        CustomSplitterHandle *customHandle = qobject_cast<CustomSplitterHandle *>(handle(i));
        if (customHandle) {
            customHandle->setColor(m_handleColor);
        }
    }
}

void CustomSplitter::setHandleWidth(int width)
{
    QSplitter::setHandleWidth(width);
    m_handleLineWidth = width / 3; // 线宽默认为手柄宽度的1/3

    // 更新所有现有的分隔线句柄
    for (int i = 0; i < count() - 1; ++i) {
        CustomSplitterHandle *customHandle = qobject_cast<CustomSplitterHandle *>(handle(i));
        if (customHandle) {
            customHandle->setLineWidth(m_handleLineWidth);
        }
    }
}

void CustomSplitter::setHandleImage(const QPixmap &pixmap)
{
    m_handlePixmap = pixmap;

    // 更新所有现有的分隔线句柄
    for (int i = 0; i < count() - 1; ++i) {
        CustomSplitterHandle *customHandle = qobject_cast<CustomSplitterHandle *>(handle(i));
        if (customHandle) {
            customHandle->setImage(m_handlePixmap);
        }
    }
}

void CustomSplitter::setHandleDashed(bool dashed)
{
    m_handleDashed = dashed;

    // 更新所有现有的分隔线句柄
    for (int i = 0; i < count() - 1; ++i) {
        CustomSplitterHandle *customHandle = qobject_cast<CustomSplitterHandle *>(handle(i));
        if (customHandle) {
            customHandle->setDashed(m_handleDashed);
        }
    }
}

void CustomSplitter::setHandleDashPattern(const QVector<qreal> &pattern)
{
    m_handleDashPattern = pattern;

    // 更新所有现有的分隔线句柄
    for (int i = 0; i < count() - 1; ++i) {
        CustomSplitterHandle *customHandle = qobject_cast<CustomSplitterHandle *>(handle(i));
        if (customHandle) {
            customHandle->setDashPattern(m_handleDashPattern);
        }
    }
}

QSplitterHandle *CustomSplitter::createHandle()
{
    CustomSplitterHandle *customHandle = new CustomSplitterHandle(orientation(), this);
    customHandle->setColor(m_handleColor);
    customHandle->setLineWidth(m_handleLineWidth);

    if (!m_handlePixmap.isNull()) {
        customHandle->setImage(m_handlePixmap);
    }

    customHandle->setDashed(m_handleDashed);
    customHandle->setDashPattern(m_handleDashPattern);

    return customHandle;
}
