<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OptionsDialog</class>
 <widget class="QDialog" name="OptionsDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>719</width>
    <height>719</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_17">
   <item row="0" column="0">
    <widget class="QTabWidget" name="tabWidget">
     <property name="currentIndex">
      <number>2</number>
     </property>
     <widget class="QWidget" name="tab">
      <attribute name="title">
       <string>外观</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_3">
       <item row="0" column="0">
        <layout class="QGridLayout" name="gridLayout_2">
         <item row="2" column="0">
          <layout class="QHBoxLayout" name="horizontalLayout_4">
           <item>
            <widget class="QLabel" name="label_3">
             <property name="text">
              <string>坐标轴标签字体:</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="label_axisLabelFont">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="btn_setAxisLabelFont">
             <property name="text">
              <string>设置字体</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item row="3" column="0">
          <layout class="QHBoxLayout" name="horizontalLayout_6">
           <item>
            <widget class="QLabel" name="label_4">
             <property name="text">
              <string>自动标签字体:</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="label_autoLabelFont">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="btn_setAutoLabelFont">
             <property name="text">
              <string>设置字体</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item row="4" column="0">
          <layout class="QHBoxLayout" name="horizontalLayout_7">
           <item>
            <widget class="QLabel" name="label_5">
             <property name="text">
              <string>手动标注默认字体:</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="label_customLabelDefalut">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="btn_setCustomLabelDefalut">
             <property name="text">
              <string>设置字体</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item row="1" column="0">
          <layout class="QHBoxLayout" name="horizontalLayout_3">
           <item>
            <widget class="QLabel" name="label_2">
             <property name="text">
              <string>坐标轴标题字体:</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="label_axisTitleFont">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="btn_setAxisTitleFont">
             <property name="text">
              <string>设置字体</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item row="0" column="0">
          <layout class="QHBoxLayout" name="horizontalLayout_2">
           <item>
            <widget class="QLabel" name="label">
             <property name="text">
              <string>图层标题字体:</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="label_titleFont">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="btn_setTitleFont">
             <property name="text">
              <string>设置字体</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item row="5" column="0">
          <layout class="QHBoxLayout" name="horizontalLayout_5">
           <item>
            <widget class="QSpinBox" name="spinBox_lineWidth">
             <property name="suffix">
              <string>px</string>
             </property>
             <property name="prefix">
              <string>图谱线宽(1~6): </string>
             </property>
             <property name="minimum">
              <number>1</number>
             </property>
             <property name="maximum">
              <number>6</number>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </item>
       <item row="1" column="0">
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_2">
      <attribute name="title">
       <string>峰识别与标记</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_4">
       <item row="1" column="0">
        <widget class="QStackedWidget" name="stackedWidget_peak">
         <property name="currentIndex">
          <number>0</number>
         </property>
         <widget class="QWidget" name="page">
          <layout class="QGridLayout" name="gridLayout_6">
           <item row="0" column="0">
            <layout class="QGridLayout" name="gridLayout_5">
             <item row="0" column="1">
              <widget class="QComboBox" name="comboBox_label_Ch">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <item>
                <property name="text">
                 <string>空白</string>
                </property>
               </item>
               <item>
                <property name="text">
                 <string>时间</string>
                </property>
               </item>
               <item>
                <property name="text">
                 <string>峰面积</string>
                </property>
               </item>
               <item>
                <property name="text">
                 <string>峰高</string>
                </property>
               </item>
               <item>
                <property name="text">
                 <string>信噪比</string>
                </property>
               </item>
               <item>
                <property name="text">
                 <string>全部显示</string>
                </property>
               </item>
              </widget>
             </item>
             <item row="3" column="0">
              <widget class="QLabel" name="label_9">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="text">
                <string>峰拆分系数:</string>
               </property>
              </widget>
             </item>
             <item row="4" column="0">
              <widget class="QLabel" name="label_19">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="text">
                <string>基线校正拟合级数:</string>
               </property>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QLabel" name="label_7">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="text">
                <string>峰阈值:</string>
               </property>
              </widget>
             </item>
             <item row="0" column="0">
              <widget class="QLabel" name="label_6">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="text">
                <string>峰标签字段:</string>
               </property>
              </widget>
             </item>
             <item row="2" column="0">
              <widget class="QLabel" name="label_8">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="text">
                <string>噪音比例:</string>
               </property>
              </widget>
             </item>
             <item row="3" column="1">
              <widget class="QSpinBox" name="spinBox_splitValue">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="suffix">
                <string>points</string>
               </property>
               <property name="minimum">
                <number>1</number>
               </property>
               <property name="maximum">
                <number>5</number>
               </property>
              </widget>
             </item>
             <item row="5" column="0">
              <widget class="QLabel" name="label_20">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="text">
                <string>基线校正残差值:</string>
               </property>
              </widget>
             </item>
             <item row="4" column="1">
              <widget class="QSpinBox" name="spinBox_fittingLevel">
               <property name="suffix">
                <string>级</string>
               </property>
               <property name="minimum">
                <number>2</number>
               </property>
               <property name="maximum">
                <number>8</number>
               </property>
               <property name="value">
                <number>3</number>
               </property>
              </widget>
             </item>
             <item row="5" column="1">
              <widget class="QSpinBox" name="spinBox_diff_Ch">
               <property name="suffix">
                <string>%</string>
               </property>
               <property name="minimum">
                <number>1</number>
               </property>
               <property name="maximum">
                <number>30</number>
               </property>
               <property name="value">
                <number>5</number>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QSpinBox" name="spinBox_threshold">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="suffix">
                <string>%</string>
               </property>
               <property name="minimum">
                <number>1</number>
               </property>
               <property name="value">
                <number>5</number>
               </property>
              </widget>
             </item>
             <item row="2" column="1">
              <widget class="QSpinBox" name="spinBox_noise">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="suffix">
                <string>%</string>
               </property>
               <property name="minimum">
                <number>1</number>
               </property>
               <property name="value">
                <number>30</number>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item row="1" column="0">
            <spacer name="verticalSpacer_2">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="page_2">
          <layout class="QGridLayout" name="gridLayout_8">
           <item row="0" column="0">
            <layout class="QGridLayout" name="gridLayout_7" rowstretch="0,0,0,1" rowminimumheight="0,0,0,0">
             <item row="0" column="1">
              <widget class="QComboBox" name="comboBox_label_mass">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <item>
                <property name="text">
                 <string>空白</string>
                </property>
               </item>
               <item>
                <property name="text">
                 <string>m/z</string>
                </property>
               </item>
               <item>
                <property name="text">
                 <string>峰面积</string>
                </property>
               </item>
               <item>
                <property name="text">
                 <string>峰高</string>
                </property>
               </item>
               <item>
                <property name="text">
                 <string>全部显示</string>
                </property>
               </item>
              </widget>
             </item>
             <item row="0" column="0">
              <widget class="QLabel" name="label_12">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="text">
                <string>峰标签字段:</string>
               </property>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QLabel" name="label_11">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="text">
                <string>峰阈值:</string>
               </property>
              </widget>
             </item>
             <item row="2" column="0">
              <widget class="QLabel" name="label_13">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="text">
                <string>质心高度比例:</string>
               </property>
              </widget>
             </item>
             <item row="2" column="1">
              <widget class="QSpinBox" name="spinBox_centroidHeightPercentage">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="suffix">
                <string>%</string>
               </property>
               <property name="minimum">
                <number>20</number>
               </property>
               <property name="maximum">
                <number>91</number>
               </property>
               <property name="value">
                <number>50</number>
               </property>
              </widget>
             </item>
             <item row="3" column="0">
              <spacer name="verticalSpacer_3">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>40</height>
                </size>
               </property>
              </spacer>
             </item>
             <item row="1" column="1">
              <widget class="QSpinBox" name="spinBox_threshold_mass">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="suffix">
                <string>%</string>
               </property>
               <property name="minimum">
                <number>1</number>
               </property>
               <property name="value">
                <number>5</number>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="page_3">
          <layout class="QGridLayout" name="gridLayout_10">
           <item row="0" column="0">
            <layout class="QGridLayout" name="gridLayout_9">
             <item row="0" column="0">
              <widget class="QLabel" name="label_15">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="text">
                <string>峰标签字段:</string>
               </property>
              </widget>
             </item>
             <item row="6" column="0">
              <spacer name="verticalSpacer_4">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>40</height>
                </size>
               </property>
              </spacer>
             </item>
             <item row="1" column="1">
              <widget class="QSpinBox" name="spinBox_threshold_DAD">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="suffix">
                <string>%</string>
               </property>
               <property name="minimum">
                <number>1</number>
               </property>
               <property name="value">
                <number>5</number>
               </property>
              </widget>
             </item>
             <item row="4" column="0">
              <widget class="QLabel" name="label_17">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="text">
                <string>基线校正拟合级数:</string>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QComboBox" name="comboBox_label_DAD">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <item>
                <property name="text">
                 <string>空白</string>
                </property>
               </item>
               <item>
                <property name="text">
                 <string>波长</string>
                </property>
               </item>
               <item>
                <property name="text">
                 <string>峰面积</string>
                </property>
               </item>
               <item>
                <property name="text">
                 <string>峰高</string>
                </property>
               </item>
               <item>
                <property name="text">
                 <string>信噪比</string>
                </property>
               </item>
               <item>
                <property name="text">
                 <string>全部显示</string>
                </property>
               </item>
              </widget>
             </item>
             <item row="3" column="1">
              <widget class="QSpinBox" name="spinBox_splitValue_DAD">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="suffix">
                <string>points</string>
               </property>
               <property name="minimum">
                <number>1</number>
               </property>
               <property name="maximum">
                <number>5</number>
               </property>
              </widget>
             </item>
             <item row="2" column="0">
              <widget class="QLabel" name="label_16">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="text">
                <string>噪音比例:</string>
               </property>
              </widget>
             </item>
             <item row="4" column="1">
              <widget class="QSpinBox" name="spinBox_fittingLevel_DAD">
               <property name="suffix">
                <string>级</string>
               </property>
               <property name="minimum">
                <number>1</number>
               </property>
               <property name="maximum">
                <number>8</number>
               </property>
               <property name="singleStep">
                <number>1</number>
               </property>
               <property name="value">
                <number>3</number>
               </property>
              </widget>
             </item>
             <item row="3" column="0">
              <widget class="QLabel" name="label_10">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="text">
                <string>峰拆分系数:</string>
               </property>
              </widget>
             </item>
             <item row="2" column="1">
              <widget class="QSpinBox" name="spinBox_noise_DAD">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimum">
                <number>1</number>
               </property>
               <property name="value">
                <number>30</number>
               </property>
               <property name="displayIntegerBase">
                <number>10</number>
               </property>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QLabel" name="label_14">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="text">
                <string>峰阈值:</string>
               </property>
              </widget>
             </item>
             <item row="5" column="0">
              <widget class="QLabel" name="label_18">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="text">
                <string>基线校正拟合级数:</string>
               </property>
              </widget>
             </item>
             <item row="5" column="1">
              <widget class="QSpinBox" name="spinBox_diff_DAD">
               <property name="suffix">
                <string>%</string>
               </property>
               <property name="minimum">
                <number>1</number>
               </property>
               <property name="maximum">
                <number>30</number>
               </property>
               <property name="singleStep">
                <number>1</number>
               </property>
               <property name="value">
                <number>5</number>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </widget>
       </item>
       <item row="0" column="0">
        <layout class="QHBoxLayout" name="horizontalLayout_8" stretch="0,0">
         <item>
          <widget class="QComboBox" name="comboBox_spectra">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="sizeAdjustPolicy">
            <enum>QComboBox::AdjustToContents</enum>
           </property>
           <item>
            <property name="text">
             <string>Chromatograms</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>Mass Spectra</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>DAD Spectra</string>
            </property>
           </item>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_3">
      <attribute name="title">
       <string>寻峰参数</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_14">
       <item row="0" column="0">
        <widget class="QTabWidget" name="tabWidget_integral_options">
         <property name="currentIndex">
          <number>0</number>
         </property>
         <widget class="QWidget" name="tab_4">
          <attribute name="title">
           <string>液相积分参数</string>
          </attribute>
          <layout class="QGridLayout" name="gridLayout_19">
           <item row="0" column="0">
            <layout class="QGridLayout" name="gridLayout" rowstretch="1,0,0,0">
             <item row="3" column="0">
              <widget class="QGroupBox" name="groupBox_find_peak">
               <property name="title">
                <string>寻峰参数</string>
               </property>
               <layout class="QGridLayout" name="gridLayout_15">
                <item row="0" column="1">
                 <widget class="QSpinBox" name="spinBox_noise_window">
                  <property name="minimum">
                   <number>10</number>
                  </property>
                  <property name="maximum">
                   <number>50</number>
                  </property>
                 </widget>
                </item>
                <item row="1" column="0">
                 <widget class="QLabel" name="label_30">
                  <property name="text">
                   <string>最小峰宽(2~20)</string>
                  </property>
                 </widget>
                </item>
                <item row="1" column="1">
                 <widget class="QSpinBox" name="spinBox_min_peak_width">
                  <property name="minimum">
                   <number>2</number>
                  </property>
                  <property name="maximum">
                   <number>20</number>
                  </property>
                  <property name="value">
                   <number>2</number>
                  </property>
                 </widget>
                </item>
                <item row="3" column="0">
                 <widget class="QLabel" name="label_32">
                  <property name="text">
                   <string>最小峰面积(0=自动)</string>
                  </property>
                 </widget>
                </item>
                <item row="0" column="0">
                 <widget class="QLabel" name="label_29">
                  <property name="text">
                   <string>噪声窗口(10~50)</string>
                  </property>
                 </widget>
                </item>
                <item row="2" column="0">
                 <widget class="QLabel" name="label_31">
                  <property name="text">
                   <string>斜率因子(1.0~5.0)</string>
                  </property>
                 </widget>
                </item>
                <item row="2" column="1">
                 <widget class="QDoubleSpinBox" name="doubleSpinBox_slope">
                  <property name="decimals">
                   <number>1</number>
                  </property>
                  <property name="minimum">
                   <double>1.000000000000000</double>
                  </property>
                  <property name="maximum">
                   <double>5.000000000000000</double>
                  </property>
                 </widget>
                </item>
                <item row="4" column="0">
                 <widget class="QLabel" name="label_33">
                  <property name="text">
                   <string>最小峰高(0=自动)</string>
                  </property>
                 </widget>
                </item>
                <item row="3" column="1">
                 <widget class="QLineEdit" name="lineEdit_min_peak_area"/>
                </item>
                <item row="4" column="1">
                 <widget class="QLineEdit" name="lineEdit_min_peak_height"/>
                </item>
               </layout>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QGroupBox" name="groupBox_smooth">
               <property name="title">
                <string>平滑参数</string>
               </property>
               <layout class="QGridLayout" name="gridLayout_12">
                <item row="1" column="0">
                 <layout class="QGridLayout" name="gridLayout_11" columnstretch="0,1">
                  <item row="0" column="1">
                   <widget class="QComboBox" name="comboBox_smooth">
                    <item>
                     <property name="text">
                      <string>Average</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>Gaussian</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>S_Golay</string>
                     </property>
                    </item>
                   </widget>
                  </item>
                  <item row="1" column="1">
                   <widget class="QComboBox" name="comboBox_window_width">
                    <item>
                     <property name="text">
                      <string>3</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>5</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>7</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>9</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>11</string>
                     </property>
                    </item>
                   </widget>
                  </item>
                  <item row="1" column="0">
                   <widget class="QLabel" name="label_22">
                    <property name="text">
                     <string>窗口大小</string>
                    </property>
                   </widget>
                  </item>
                  <item row="2" column="1">
                   <widget class="QDoubleSpinBox" name="doubleSpinBox_gaussian_sigma">
                    <property name="decimals">
                     <number>1</number>
                    </property>
                    <property name="minimum">
                     <double>0.500000000000000</double>
                    </property>
                    <property name="maximum">
                     <double>3.000000000000000</double>
                    </property>
                    <property name="singleStep">
                     <double>0.500000000000000</double>
                    </property>
                   </widget>
                  </item>
                  <item row="0" column="0">
                   <widget class="QLabel" name="label_21">
                    <property name="text">
                     <string>平滑方法</string>
                    </property>
                   </widget>
                  </item>
                  <item row="2" column="0">
                   <widget class="QLabel" name="label_23">
                    <property name="text">
                     <string>高斯σ</string>
                    </property>
                   </widget>
                  </item>
                  <item row="3" column="0">
                   <widget class="QLabel" name="label_24">
                    <property name="text">
                     <string>S-G多项式阶数</string>
                    </property>
                   </widget>
                  </item>
                  <item row="3" column="1">
                   <widget class="QSpinBox" name="spinBox_polyOrder">
                    <property name="minimum">
                     <number>1</number>
                    </property>
                    <property name="maximum">
                     <number>4</number>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </item>
               </layout>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QGroupBox" name="groupBox_baseline">
               <property name="title">
                <string>基线校正参数</string>
               </property>
               <layout class="QGridLayout" name="gridLayout_13">
                <item row="2" column="0">
                 <widget class="QLabel" name="label_27">
                  <property name="text">
                   <string>ALS p(0.001-0.1)</string>
                  </property>
                 </widget>
                </item>
                <item row="0" column="1">
                 <widget class="QComboBox" name="comboBox_baseline">
                  <item>
                   <property name="text">
                    <string>三次多项式</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>ALS</string>
                   </property>
                  </item>
                 </widget>
                </item>
                <item row="1" column="0">
                 <widget class="QLabel" name="label_26">
                  <property name="text">
                   <string>ALS λ(1e3~1e7)</string>
                  </property>
                 </widget>
                </item>
                <item row="0" column="0">
                 <widget class="QLabel" name="label_25">
                  <property name="text">
                   <string>基线方法选择</string>
                  </property>
                 </widget>
                </item>
                <item row="3" column="0">
                 <widget class="QLabel" name="label_28">
                  <property name="text">
                   <string>ALS迭代次数(5~20)</string>
                  </property>
                 </widget>
                </item>
                <item row="1" column="1">
                 <widget class="QLineEdit" name="lineEdit_als_lambda"/>
                </item>
                <item row="2" column="1">
                 <widget class="QLineEdit" name="lineEdit_als_p"/>
                </item>
                <item row="3" column="1">
                 <widget class="QSpinBox" name="spinBox_als_count">
                  <property name="minimum">
                   <number>5</number>
                  </property>
                  <property name="maximum">
                   <number>20</number>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item row="0" column="0" colspan="2">
              <widget class="QCheckBox" name="checkBox_use_default">
               <property name="text">
                <string>使用默认值</string>
               </property>
              </widget>
             </item>
             <item row="3" column="1">
              <layout class="QGridLayout" name="gridLayout_18">
               <item row="1" column="0">
                <spacer name="verticalSpacer_5">
                 <property name="orientation">
                  <enum>Qt::Vertical</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>20</width>
                   <height>40</height>
                  </size>
                 </property>
                </spacer>
               </item>
               <item row="0" column="0">
                <widget class="QGroupBox" name="groupBox">
                 <property name="title">
                  <string>SNR计算参数</string>
                 </property>
                 <layout class="QGridLayout" name="gridLayout_16" columnstretch="0">
                  <item row="0" column="0">
                   <widget class="QSpinBox" name="spinBox_folderWidthOfNoise">
                    <property name="prefix">
                     <string>噪声窗口倍数</string>
                    </property>
                    <property name="value">
                     <number>10</number>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="tab_5">
          <attribute name="title">
           <string>质谱积分参数</string>
          </attribute>
          <layout class="QGridLayout" name="gridLayout_27">
           <item row="0" column="0">
            <layout class="QGridLayout" name="gridLayout_20" rowstretch="1,0,0,0">
             <item row="3" column="0">
              <widget class="QGroupBox" name="groupBox_find_peak_2">
               <property name="title">
                <string>寻峰参数</string>
               </property>
               <layout class="QGridLayout" name="gridLayout_21">
                <item row="0" column="1">
                 <widget class="QSpinBox" name="spinBox_noise_window_mass">
                  <property name="minimum">
                   <number>10</number>
                  </property>
                  <property name="maximum">
                   <number>50</number>
                  </property>
                 </widget>
                </item>
                <item row="1" column="0">
                 <widget class="QLabel" name="label_34">
                  <property name="text">
                   <string>最小峰宽(2~20)</string>
                  </property>
                 </widget>
                </item>
                <item row="1" column="1">
                 <widget class="QSpinBox" name="spinBox_min_peak_width_mass">
                  <property name="minimum">
                   <number>2</number>
                  </property>
                  <property name="maximum">
                   <number>20</number>
                  </property>
                  <property name="value">
                   <number>2</number>
                  </property>
                 </widget>
                </item>
                <item row="3" column="0">
                 <widget class="QLabel" name="label_35">
                  <property name="text">
                   <string>最小峰面积(0=自动)</string>
                  </property>
                 </widget>
                </item>
                <item row="0" column="0">
                 <widget class="QLabel" name="label_36">
                  <property name="text">
                   <string>噪声窗口(10~50)</string>
                  </property>
                 </widget>
                </item>
                <item row="2" column="0">
                 <widget class="QLabel" name="label_37">
                  <property name="text">
                   <string>斜率因子(1.0~5.0)</string>
                  </property>
                 </widget>
                </item>
                <item row="2" column="1">
                 <widget class="QDoubleSpinBox" name="doubleSpinBox_slope_mass">
                  <property name="decimals">
                   <number>1</number>
                  </property>
                  <property name="minimum">
                   <double>1.000000000000000</double>
                  </property>
                  <property name="maximum">
                   <double>5.000000000000000</double>
                  </property>
                 </widget>
                </item>
                <item row="4" column="0">
                 <widget class="QLabel" name="label_38">
                  <property name="text">
                   <string>最小峰高(0=自动)</string>
                  </property>
                 </widget>
                </item>
                <item row="3" column="1">
                 <widget class="QLineEdit" name="lineEdit_min_peak_area_mass"/>
                </item>
                <item row="4" column="1">
                 <widget class="QLineEdit" name="lineEdit_min_peak_height_mass"/>
                </item>
               </layout>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QGroupBox" name="groupBox_smooth_2">
               <property name="title">
                <string>平滑参数</string>
               </property>
               <layout class="QGridLayout" name="gridLayout_22">
                <item row="1" column="0">
                 <layout class="QGridLayout" name="gridLayout_23" columnstretch="0,1">
                  <item row="0" column="1">
                   <widget class="QComboBox" name="comboBox_smooth_mass">
                    <item>
                     <property name="text">
                      <string>Average</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>Gaussian</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>S_Golay</string>
                     </property>
                    </item>
                   </widget>
                  </item>
                  <item row="1" column="1">
                   <widget class="QComboBox" name="comboBox_window_width_mass">
                    <item>
                     <property name="text">
                      <string>3</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>5</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>7</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>9</string>
                     </property>
                    </item>
                    <item>
                     <property name="text">
                      <string>11</string>
                     </property>
                    </item>
                   </widget>
                  </item>
                  <item row="1" column="0">
                   <widget class="QLabel" name="label_39">
                    <property name="text">
                     <string>窗口大小</string>
                    </property>
                   </widget>
                  </item>
                  <item row="2" column="1">
                   <widget class="QDoubleSpinBox" name="doubleSpinBox_gaussian_sigma_mass">
                    <property name="decimals">
                     <number>1</number>
                    </property>
                    <property name="minimum">
                     <double>0.500000000000000</double>
                    </property>
                    <property name="maximum">
                     <double>3.000000000000000</double>
                    </property>
                    <property name="singleStep">
                     <double>0.500000000000000</double>
                    </property>
                   </widget>
                  </item>
                  <item row="0" column="0">
                   <widget class="QLabel" name="label_40">
                    <property name="text">
                     <string>平滑方法</string>
                    </property>
                   </widget>
                  </item>
                  <item row="2" column="0">
                   <widget class="QLabel" name="label_41">
                    <property name="text">
                     <string>高斯σ</string>
                    </property>
                   </widget>
                  </item>
                  <item row="3" column="0">
                   <widget class="QLabel" name="label_42">
                    <property name="text">
                     <string>S-G多项式阶数</string>
                    </property>
                   </widget>
                  </item>
                  <item row="3" column="1">
                   <widget class="QSpinBox" name="spinBox_polyOrder_mass">
                    <property name="minimum">
                     <number>1</number>
                    </property>
                    <property name="maximum">
                     <number>4</number>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </item>
               </layout>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QGroupBox" name="groupBox_baseline_2">
               <property name="title">
                <string>基线校正参数</string>
               </property>
               <layout class="QGridLayout" name="gridLayout_24">
                <item row="2" column="0">
                 <widget class="QLabel" name="label_43">
                  <property name="text">
                   <string>ALS p(0.001-0.1)</string>
                  </property>
                 </widget>
                </item>
                <item row="0" column="1">
                 <widget class="QComboBox" name="comboBox_baseline_mass">
                  <item>
                   <property name="text">
                    <string>三次多项式</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>ALS</string>
                   </property>
                  </item>
                 </widget>
                </item>
                <item row="1" column="0">
                 <widget class="QLabel" name="label_44">
                  <property name="text">
                   <string>ALS λ(1e3~1e7)</string>
                  </property>
                 </widget>
                </item>
                <item row="0" column="0">
                 <widget class="QLabel" name="label_45">
                  <property name="text">
                   <string>基线方法选择</string>
                  </property>
                 </widget>
                </item>
                <item row="3" column="0">
                 <widget class="QLabel" name="label_46">
                  <property name="text">
                   <string>ALS迭代次数(5~20)</string>
                  </property>
                 </widget>
                </item>
                <item row="1" column="1">
                 <widget class="QLineEdit" name="lineEdit_als_lambda_mass"/>
                </item>
                <item row="2" column="1">
                 <widget class="QLineEdit" name="lineEdit_als_p_mass"/>
                </item>
                <item row="3" column="1">
                 <widget class="QSpinBox" name="spinBox_als_count_mass">
                  <property name="minimum">
                   <number>5</number>
                  </property>
                  <property name="maximum">
                   <number>20</number>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item row="0" column="0" colspan="2">
              <widget class="QCheckBox" name="checkBox_use_default_mass">
               <property name="text">
                <string>使用默认值</string>
               </property>
              </widget>
             </item>
             <item row="3" column="1">
              <layout class="QGridLayout" name="gridLayout_25">
               <item row="1" column="0">
                <spacer name="verticalSpacer_6">
                 <property name="orientation">
                  <enum>Qt::Vertical</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>20</width>
                   <height>40</height>
                  </size>
                 </property>
                </spacer>
               </item>
               <item row="0" column="0">
                <widget class="QGroupBox" name="groupBox_2">
                 <property name="title">
                  <string>SNR计算参数</string>
                 </property>
                 <layout class="QGridLayout" name="gridLayout_26" columnstretch="0">
                  <item row="0" column="0">
                   <widget class="QSpinBox" name="spinBox_folderWidthOfNoise_mass">
                    <property name="prefix">
                     <string>噪声窗口倍数</string>
                    </property>
                    <property name="value">
                     <number>10</number>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item row="1" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="btn_ok">
       <property name="text">
        <string>OK</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="btn_cancle">
       <property name="text">
        <string>Cancle</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="btn_reset">
       <property name="text">
        <string>Reset all options</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
