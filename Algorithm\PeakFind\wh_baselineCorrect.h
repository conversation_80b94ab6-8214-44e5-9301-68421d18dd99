﻿#pragma once
#include <vector>

/**
 * @brief 三次多项式拟合基线校正
 * @details 使用三次多项式方程(y=ax^3 + bx^2 + cx + d)拟合数据基线，并从原始信号中减去
 * @param signal 输入的原始信号数据向量
 * @return 基线校正后的信号数据向量
 */
std::vector<double> cubicBaselineCorrection(const std::vector<double>& signal);

/**
 * @brief 静态分段拟合基线校正
 * @details 将信号分成固定大小的段，每段单独应用三次多项式拟合基线校正
 * @note 此函数当前未完成实现
 * @param signal 输入的原始信号数据向量
 * @param segmentSize 每段的大小（数据点数）
 * @return 基线校正后的信号数据向量
 */
std::vector<double> segmentedBaselineCorrection(const std::vector<double>& signal, int segmentSize);

/**
 * @brief 非对称最小二乘（ALS）基线校正
 * @details 使用迭代非对称加权方法校正基线，特别适合处理含有峰值的信号
 * @param signal 原信号数据向量
 * @param lambda 控制平滑强度（越大基线越平滑）,取值范围：1e3 到 1e7
 * @param p 非对称权重（越小越保护峰值）,取值范围：0.001 到 0.1
 * @param maxIter 迭代次数（通常 5-10 次收敛），默认值为10
 * @return 减掉基线后的信号数据向量
 */
std::vector<double> alsBaselineCorrection(const std::vector<double>& signal, double lambda = 1e5, double p = 0.01, int maxIter = 10);
