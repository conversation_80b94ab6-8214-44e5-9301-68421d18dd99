#include "ticchartdata.h"
#include "masschartdata.h"
#include "xicchartdata.h"
#include <QDebug>
#include <QThread>
#include <QCoreApplication>
#include <QMetaObject>

TicChartData::TicChartData(QString ParamPath, int eventId, GlobalEnums::IonMode ionMode, GlobalEnums::ScanMode scanMode, QString dataProcess, QString dataName,
                           QString sampleName, QObject *parent)
    : LxChartData(ParamPath, GlobalEnums::TrackType::TIC, ionMode, scanMode, dataProcess, dataName, sampleName, eventId, parent), m_eventId(eventId)
{
    // qDebug() << "TicChartData::TicChartData: 创建TIC数据，事件ID:" << m_eventId << "，路径:" << ParamPath << "，对象地址:" << this;
    // qDebug() << "TicChartData::TicChartData: m_qstr_paramPath设置为:" << m_qstr_paramPath;
}

TicChartData::~TicChartData()
{
    qDebug() << "TicChartData::~TicChartData: 开始析构，事件ID:" << m_eventId << "，当前线程:" << QThread::currentThread();

    // 先删除MASS数据
    deleteMassData();

    // 再删除所有XIC数据
    deleteAllXicData();

    qDebug() << "TicChartData::~TicChartData: 析构完成，事件ID:" << m_eventId;
}

// 🎯 新的线程安全方法实现
bool TicChartData::hasMassData() const
{
    QReadLocker locker(&m_massDataLock);
    return m_massDataPtr != nullptr;
}

std::shared_ptr<MassChartData> TicChartData::getMassDataPtr() const
{
    QReadLocker locker(&m_massDataLock);
    return m_massDataPtr;
}

MassChartData *TicChartData::createMassDataSafe()
{
    // 🎯 防止重复创建的原子检查
    if (m_massDataLoading.exchange(true))
    {
        qDebug() << "TicChartData::createMassDataSafe: MASS数据正在加载中，事件ID:" << m_eventId;
        // 等待加载完成
        while (m_massDataLoading.load() && !m_massDataReady.load())
        {
            QThread::msleep(1);
        }
        QReadLocker locker(&m_massDataLock);
        return m_massData;
    }

    QWriteLocker locker(&m_massDataLock);

    // 双重检查，防止竞态条件
    if (m_massDataPtr)
    {
        qDebug() << "TicChartData::createMassDataSafe: MASS数据已存在，事件ID:" << m_eventId;
        m_massDataLoading = false;
        return m_massData;
    }

    try
    {
        // 🎯 使用智能指针创建，避免内存泄漏
        m_massDataPtr = std::make_shared<MassChartData>(
            m_qstr_paramPath, ionMode, scanMode, dataProcess,
            dataName, sampleName, m_eventId, this);

        // 保持兼容性指针
        m_massData = m_massDataPtr.get();
        m_massData->setTic_event_id(m_eventId);

        m_massDataReady = true;
        qDebug() << "TicChartData::createMassDataSafe: 成功创建MASS数据，事件ID:" << m_eventId;

        // 🎯 发出数据加载完成信号
        emit massDataLoadCompleted(m_eventId, m_massData);
    }
    catch (const std::exception &e)
    {
        QString errorMsg = QString("创建MASS数据异常: %1").arg(e.what());
        qDebug() << "TicChartData::createMassDataSafe:" << errorMsg;
        m_massDataPtr.reset();
        m_massData = nullptr;

        // 🎯 发出数据加载失败信号
        emit massDataLoadFailed(m_eventId, errorMsg);
    }
    catch (...)
    {
        QString errorMsg = "创建MASS数据未知异常";
        qDebug() << "TicChartData::createMassDataSafe:" << errorMsg;
        m_massDataPtr.reset();
        m_massData = nullptr;

        // 🎯 发出数据加载失败信号
        emit massDataLoadFailed(m_eventId, errorMsg);
    }

    m_massDataLoading = false;
    return m_massData;
}

// 🎯 兼容性方法（保持原有接口）
MassChartData *TicChartData::createMassData()
{
    if (m_massData)
    {
        qDebug() << "TicChartData::createMassData: MASS数据已存在，事件ID:" << m_eventId;
        return m_massData;
    }

    // qDebug() << "创建MASS数据，事件ID:" << m_eventId;

    // 检查是否在主线程中
    if (QThread::currentThread() == QCoreApplication::instance()->thread())
    {
        // 在主线程中，直接创建
        m_massData = new MassChartData(m_qstr_paramPath, ionMode, scanMode, dataProcess, dataName, sampleName, m_eventId, this);
        m_massData->setTic_event_id(m_eventId);
        qDebug() << "TicChartData::createMassData: 在主线程中创建MASS数据";
    }
    else
    {
        // 不在主线程中，使用QMetaObject::invokeMethod在主线程中创建
        // 🎯 修复：使用值捕获避免this指针悬空问题
        QString paramPath = m_qstr_paramPath;
        int eventId = m_eventId;
        GlobalEnums::IonMode ionModeLocal = ionMode;
        GlobalEnums::ScanMode scanModeLocal = scanMode;
        QString dataProcessLocal = dataProcess;
        QString dataNameLocal = dataName;
        QString sampleNameLocal = sampleName;

        qDebug() << "TicChartData::createMassData: 不在主线程，使用invokeMethod创建MASS，事件ID:" << eventId;

        // 使用值捕获确保线程安全
        QMetaObject::invokeMethod(this, [this, paramPath, eventId, ionModeLocal, scanModeLocal, dataProcessLocal, dataNameLocal, sampleNameLocal]()
                                  {
                                      // 再次检查m_massData是否已被其他线程创建
                                      if (!m_massData) {
                                          m_massData = new MassChartData(paramPath, ionModeLocal, scanModeLocal, dataProcessLocal, dataNameLocal, sampleNameLocal, eventId, this);
                                          m_massData->setTic_event_id(eventId);
                                          qDebug() << "TicChartData::createMassData: 在主线程中通过invokeMethod创建MASS数据，事件ID:" << eventId;
                                      } else {
                                          qDebug() << "TicChartData::createMassData: MASS数据已存在，跳过创建，事件ID:" << eventId;
                                      } }, Qt::BlockingQueuedConnection);
    }

    if (m_massData)
    {
        // qDebug() << "MASS数据创建成功，事件ID:" << m_eventId;
    }
    else
    {
        qDebug() << "TicChartData::createMassData: 创建MASS数据失败，事件ID:" << m_eventId;
    }

    return m_massData;
}

void TicChartData::deleteMassDataSafe()
{
    QWriteLocker locker(&m_massDataLock);

    if (m_massDataPtr)
    {
        qDebug() << "TicChartData::deleteMassDataSafe: 删除MASS数据，事件ID:" << m_eventId
                 << "，当前线程:" << QThread::currentThread();

        try
        {
            // 🎯 智能指针自动管理内存，无需手动delete
            m_massDataPtr.reset();
            m_massData = nullptr;
            m_massDataReady = false;
            m_massDataLoading = false;

            qDebug() << "TicChartData::deleteMassDataSafe: MASS数据删除成功";
        }
        catch (const std::exception &e)
        {
            qDebug() << "TicChartData::deleteMassDataSafe: 删除MASS数据时发生标准异常:" << e.what();
        }
        catch (...)
        {
            qDebug() << "TicChartData::deleteMassDataSafe: 删除MASS数据时发生未知异常";
        }
    }
    else
    {
        qDebug() << "TicChartData::deleteMassDataSafe: MASS数据已为空，事件ID:" << m_eventId;
    }
}

// 🎯 兼容性方法（保持原有接口）
void TicChartData::deleteMassData()
{
    if (m_massData)
    {
        qDebug() << "TicChartData::deleteMassData: 删除MASS数据，事件ID:" << m_eventId
                 << "，当前线程:" << QThread::currentThread();

        // 🎯 修复：增强MASS数据删除的安全性
        MassChartData *massToDelete = m_massData;
        m_massData = nullptr; // 立即设置为nullptr，避免重复删除

        try
        {
            // 检查对象是否仍然有效
            if (massToDelete)
            {
                qDebug() << "TicChartData::deleteMassData: 准备删除MASS对象，地址:" << static_cast<void *>(massToDelete);
                delete massToDelete;
                qDebug() << "TicChartData::deleteMassData: MASS数据删除成功";
            }
        }
        catch (const std::exception &e)
        {
            qDebug() << "TicChartData::deleteMassData: 删除MASS数据时发生标准异常:" << e.what();
        }
        catch (...)
        {
            qDebug() << "TicChartData::deleteMassData: 删除MASS数据时发生未知异常";
        }
    }
    else
    {
        qDebug() << "TicChartData::deleteMassData: MASS数据已为空，事件ID:" << m_eventId;
    }
}

XicChartData *TicChartData::getXicData(const QUuid &xicId) const
{
    return m_xicDataMap.value(xicId, nullptr);
}

XicChartData *TicChartData::getLatestXicData() const
{
    return m_xicDataVector.isEmpty() ? nullptr : m_xicDataVector.last();
}

QUuid TicChartData::createXicData()
{
    QUuid xicId = QUuid::createUuid();

    qDebug() << "TicChartData::createXicData: 准备创建XIC数据，事件ID:" << m_eventId
             << "，XIC ID:" << xicId.toString() << "，当前线程:" << QThread::currentThread();

    XicChartData *xicData = nullptr;

    // 检查是否在主线程中
    if (QThread::currentThread() == QCoreApplication::instance()->thread())
    {
        // 在主线程中，直接创建
        xicData = new XicChartData(m_qstr_paramPath, ionMode, scanMode, dataProcess, dataName, sampleName, m_eventId, this);
        xicData->setTic_event_id(m_eventId);

        // 设置XIC的标题（文件路径_索引）
        // 计算下一个可用的索引，确保唯一性
        int xicIndex = 0;
        QSet<int> usedIndices;
        for (auto it = m_xicDataMap.begin(); it != m_xicDataMap.end(); ++it)
        {
            XicChartData *existingXic = it.value();
            if (existingXic)
            {
                QString title = existingXic->getTitle();
                // 从标题中提取索引（格式：路径_索引）
                int underscorePos = title.lastIndexOf('_');
                if (underscorePos != -1)
                {
                    bool ok;
                    int index = title.mid(underscorePos + 1).toInt(&ok);
                    if (ok)
                    {
                        usedIndices.insert(index);
                    }
                }
            }
        }

        // 找到第一个未使用的索引
        while (usedIndices.contains(xicIndex))
        {
            xicIndex++;
        }

        QString xicTitle = QString("%1_%2").arg(getTitle()).arg(xicIndex);
        xicData->setTitle(xicTitle);

        qDebug() << "TicChartData::createXicData: 在主线程中创建XIC数据";
    }
    else
    {
        // 不在主线程中，使用QMetaObject::invokeMethod在主线程中创建
        // qDebug() << "不在主线程，使用invokeMethod创建XIC";

        // 改回同步创建，确保对象创建成功
        bool createSuccess = false;
        QMetaObject::invokeMethod(this, [this, &xicData, &createSuccess]()
                                  {
            try {
                qDebug() << "TicChartData::createXicData: 准备创建XIC，使用paramPath:" << m_qstr_paramPath << "，事件ID:" << m_eventId;
                xicData = new XicChartData(m_qstr_paramPath, ionMode, scanMode, dataProcess, dataName, sampleName, m_eventId, this);
                xicData->setTic_event_id(m_eventId);

                // 设置XIC的标题（文件路径_索引）
                // 计算下一个可用的索引，确保唯一性
                int xicIndex = 0;
                QSet<int> usedIndices;
                for (auto it = m_xicDataMap.begin(); it != m_xicDataMap.end(); ++it)
                {
                    XicChartData *existingXic = it.value();
                    if (existingXic)
                    {
                        QString title = existingXic->getTitle();
                        // 从标题中提取索引（格式：路径_索引）
                        int underscorePos = title.lastIndexOf('_');
                        if (underscorePos != -1)
                        {
                            bool ok;
                            int index = title.mid(underscorePos + 1).toInt(&ok);
                            if (ok)
                            {
                                usedIndices.insert(index);
                            }
                        }
                    }
                }

                // 找到第一个未使用的索引
                while (usedIndices.contains(xicIndex))
                {
                    xicIndex++;
                }

                QString xicTitle = QString("%1_%2").arg(getTitle()).arg(xicIndex);
                xicData->setTitle(xicTitle);

                createSuccess = true;
                qDebug() << "TicChartData::createXicData: 在主线程中通过invokeMethod创建XIC数据成功，XIC的paramPath:" << xicData->getParamPath();
            } catch (...) {
                qDebug() << "TicChartData::createXicData: 在主线程中创建XIC数据时发生异常";
                createSuccess = false;
            } }, Qt::BlockingQueuedConnection);

        if (!createSuccess || !xicData)
        {
            qDebug() << "TicChartData::createXicData: 同步创建XIC数据失败";
            return QUuid(); // 返回无效的UUID
        }
    }

    if (xicData)
    {
        m_xicDataMap[xicId] = xicData;
        m_xicDataVector.append(xicData); // 添加到有序列表

        // 为XIC创建对应的LxChartLegend
        // XIC的命名格式：文件路径_递增编号（同一个TIC下递增）
        QString xicTitle = xicData->getTitle(); // 使用已经设置的标题

        // LxChartLegend将在AddLxChartData中统一创建，这里不需要创建

        qDebug() << "TicChartData::createXicData: 创建XIC数据成功，事件ID:" << m_eventId
                 << "，XIC ID:" << xicId.toString() << "，XIC标题:" << xicTitle
                 << "，当前XIC数量:" << m_xicDataVector.size();
    }
    else
    {
        qDebug() << "TicChartData::createXicData: 创建XIC数据失败，事件ID:" << m_eventId;
        return QUuid(); // 返回无效的UUID
    }

    return xicId;
}

bool TicChartData::deleteXicData(const QUuid &xicId)
{
    if (!m_xicDataMap.contains(xicId))
    {
        qDebug() << "TicChartData::deleteXicData: XIC ID不存在:" << xicId.toString() << "，事件ID:" << m_eventId;
        return false;
    }

    XicChartData *xicData = m_xicDataMap[xicId];
    if (xicData)
    {
        qDebug() << "TicChartData::deleteXicData: 删除XIC数据，XIC ID:" << xicId.toString()
                 << "，事件ID:" << m_eventId << "，当前线程:" << QThread::currentThread();

        // 尝试安全地获取UniqueID
        QString uniqueId = "未知";
        try
        {
            uniqueId = xicData->getUniqueID();
            qDebug() << "TicChartData::deleteXicData: XIC数据UniqueID:" << uniqueId;
        }
        catch (...)
        {
            qDebug() << "TicChartData::deleteXicData: 获取UniqueID时异常，对象可能已损坏";
        }

        try
        {
            // 从有序列表中移除
            m_xicDataVector.removeAll(xicData);

            // TIC删除时，负责删除其管理的XIC数据（这是唯一正确的删除位置）
            qDebug() << "TicChartData::deleteXicData: TIC删除时清理XIC数据";
            delete xicData;
            qDebug() << "TicChartData::deleteXicData: XIC数据删除成功";
        }
        catch (...)
        {
            qDebug() << "TicChartData::deleteXicData: 删除XIC数据时发生异常";
        }
    }

    m_xicDataMap.remove(xicId);
    qDebug() << "TicChartData::deleteXicData: XIC删除完成，剩余XIC数量:" << m_xicDataVector.size();
    return true;
}

void TicChartData::deleteAllXicData()
{
    qDebug() << "TicChartData::deleteAllXicData: 删除所有XIC数据，事件ID:" << m_eventId
             << "，当前XIC数量:" << m_xicDataMap.size();

    for (auto it = m_xicDataMap.begin(); it != m_xicDataMap.end(); ++it)
    {
        XicChartData *xicData = it.value();
        if (xicData)
        {
            try
            {
                qDebug() << "TicChartData::deleteAllXicData: 删除XIC，ID:" << it.key().toString();
                delete xicData;
            }
            catch (...)
            {
                qDebug() << "TicChartData::deleteAllXicData: 删除XIC数据时发生异常，ID:" << it.key().toString();
            }
        }
    }

    m_xicDataMap.clear();
    qDebug() << "TicChartData::deleteAllXicData: 所有XIC数据删除完成";
}
