#ifndef PEAKFINDINGCONFIGS_H
#define PEAKFINDINGCONFIGS_H
#include <qglobal.h>
#include <QFile>
#include "Globals/GlobalDefine.h"
#include "Config/configmanager.h"
//*此类用于存储峰识别标记配置信息 */////////////⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️///////////////
//*此类用于存储峰识别标记配置信息 */////////////⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️///////////////

enum class LabelFiledType
{
    // 峰标签字段
    Blank,  // 空白
    Time,   // 时间
    Area,   // 面积
    Height, // 峰高
    Noise,  // 信噪比
    MZ,
    WateLength, // 波长
    All         // 全部显示
};
// 将 LabelFiledType 转换为 uint
constexpr uint32_t to_uint(LabelFiledType type)
{
    return static_cast<uint32_t>(type);
}

// 将 uint 转换为 LabelFiledType
constexpr LabelFiledType from_uint(uint32_t value)
{
    return static_cast<LabelFiledType>(value);
}
struct PeakFindingChromatograms
{
    // 峰识别与标记-色谱图
    LabelFiledType FiledType;
    quint8 PeakThreshold;
    quint8 NoisePercent;
    quint8 PeakSplit;
    quint8 Fitting;
    quint8 Diff;

    // 默认构造函数
    PeakFindingChromatograms() : FiledType(LabelFiledType::Blank), PeakThreshold(5), NoisePercent(30), PeakSplit(1), Fitting(3), Diff(5) {}
};

struct PeakFindingMass
{
    // 峰识别与标记-质谱图
    LabelFiledType FiledType;
    quint8 PeakThreshold;
    quint8 CentroidHeightPercentage;

    // 默认构造函数
    PeakFindingMass() : FiledType(LabelFiledType::Blank), PeakThreshold(5), CentroidHeightPercentage(50) {}
};

struct PeakFindingDAD
{
    // 峰识别与标记-紫外谱图
    LabelFiledType FiledType;
    quint8 PeakThreshold;
    quint8 NoisePercent;
    quint8 PeakSplit;
    quint8 Fitting;
    quint8 Diff;

    // 默认构造函数
    PeakFindingDAD() : FiledType(LabelFiledType::Blank), PeakThreshold(5), NoisePercent(30), PeakSplit(1), Fitting(3), Diff(5) {}
};

// ==================== OptionsDialog新增页面配置结构体 ====================

/**
 * @brief 外观页面配置结构体
 */
struct AppearanceSettings
{
    // 图层标题字体设置
    QString axisLabelFontFamily;
    int axisLabelFontSize;
    bool axisLabelFontBold;
    bool axisLabelFontItalic;
    bool axisLabelFontStrikeOut;
    bool axisLabelFontUnderline;

    // 坐标轴标题字体设置
    QString autoLabelFontFamily;
    int autoLabelFontSize;
    bool autoLabelFontBold;
    bool autoLabelFontItalic;
    bool autoLabelFontStrikeOut;
    bool autoLabelFontUnderline;

    // 坐标轴标签字体设置
    QString customLabelFontFamily;
    int customLabelFontSize;
    bool customLabelFontBold;
    bool customLabelFontItalic;
    bool customLabelFontStrikeOut;
    bool customLabelFontUnderline;

    // 自动标题字体设置
    QString axisTitleFontFamily;
    int axisTitleFontSize;
    bool axisTitleFontBold;
    bool axisTitleFontItalic;
    bool axisTitleFontStrikeOut;
    bool axisTitleFontUnderline;

    // 手动标题字体设置
    QString titleFontFamily;
    int titleFontSize;
    bool titleFontBold;
    bool titleFontItalic;
    bool titleFontStrikeOut;
    bool titleFontUnderline;

    // 谱图线宽设置
    double spectrumLineWidth;

    // 默认构造函数
    AppearanceSettings()
        : axisLabelFontFamily("Microsoft YaHei"), axisLabelFontSize(9), axisLabelFontBold(false), axisLabelFontItalic(false), axisLabelFontStrikeOut(false),
          axisLabelFontUnderline(false), autoLabelFontFamily("Microsoft YaHei"), autoLabelFontSize(9), autoLabelFontBold(false), autoLabelFontItalic(false),
          autoLabelFontStrikeOut(false), autoLabelFontUnderline(false), customLabelFontFamily("Microsoft YaHei"), customLabelFontSize(9),
          customLabelFontBold(false), customLabelFontItalic(false), customLabelFontStrikeOut(false), customLabelFontUnderline(false),
          axisTitleFontFamily("Microsoft YaHei"), axisTitleFontSize(10), axisTitleFontBold(true), axisTitleFontItalic(false), axisTitleFontStrikeOut(false),
          axisTitleFontUnderline(false), titleFontFamily("Microsoft YaHei"), titleFontSize(12), titleFontBold(true), titleFontItalic(false),
          titleFontStrikeOut(false), titleFontUnderline(false), spectrumLineWidth(1.0)
    {
    }
};

/**
 * @brief 寻峰参数页面配置结构体
 */
struct PeakFindingParameters
{
    // 使用默认参数标志
    bool useDefault;

    // 平滑参数
    int smoothType;       // 平滑算法类型 (0=Average, 1=Gaussian, 2=SavitzkyGolay)
    int windowSize;       // 窗口大小
    double gaussianSigma; // 高斯σ
    int polyOrder;        // 多项式阶数

    // 基线校正参数
    int baselineType; // 基线校正类型 (0=三次多项式, 1=ALS)
    double alsLambda; // ALS λ参数
    double alsP;      // ALS p参数
    int alsMaxIter;   // ALS最大迭代次数

    // 寻峰参数
    int noiseWindow;        // 噪声窗口
    int minPeakWidth;       // 最小峰宽
    double slopeFactor;     // 斜率因子
    double minPeakArea;     // 最小峰面积
    double minPeakHeight;   // 最小峰高
    int folderWidthOfNoise; // SNR噪声窗口倍数

    // 默认构造函数
    PeakFindingParameters()
        : useDefault(true), smoothType(0), windowSize(5), gaussianSigma(1.0), polyOrder(2), baselineType(0), alsLambda(1e5), alsP(0.01), alsMaxIter(10),
          noiseWindow(20), minPeakWidth(5), slopeFactor(2.0), minPeakArea(0.0), minPeakHeight(0.0), folderWidthOfNoise(10)
    {
    }

    // 比较操作符
    bool operator!=(const PeakFindingParameters &other) const
    {
        return useDefault != other.useDefault || smoothType != other.smoothType || windowSize != other.windowSize ||
               qAbs(gaussianSigma - other.gaussianSigma) > 1e-6 || polyOrder != other.polyOrder || baselineType != other.baselineType ||
               qAbs(alsLambda - other.alsLambda) > 1e-6 || qAbs(alsP - other.alsP) > 1e-6 || alsMaxIter != other.alsMaxIter ||
               noiseWindow != other.noiseWindow || minPeakWidth != other.minPeakWidth || qAbs(slopeFactor - other.slopeFactor) > 1e-6 ||
               qAbs(minPeakArea - other.minPeakArea) > 1e-6 || qAbs(minPeakHeight - other.minPeakHeight) > 1e-6 ||
               folderWidthOfNoise != other.folderWidthOfNoise;
    }
};

/**
 * @brief 质谱寻峰参数页面配置结构体
 */
struct PeakFindingParametersMass
{
    // 使用默认参数标志
    bool useDefault_mass;

    // 平滑参数
    int smoothType_mass;       // 平滑算法类型 (0=Average, 1=Gaussian, 2=SavitzkyGolay)
    int windowSize_mass;       // 窗口大小
    double gaussianSigma_mass; // 高斯σ
    int polyOrder_mass;        // 多项式阶数

    // 基线校正参数
    int baselineType_mass; // 基线校正类型 (0=三次多项式, 1=ALS)
    double alsLambda_mass; // ALS λ参数
    double alsP_mass;      // ALS p参数
    int alsMaxIter_mass;   // ALS最大迭代次数

    // 寻峰参数
    int noiseWindow_mass;        // 噪声窗口
    int minPeakWidth_mass;       // 最小峰宽
    double slopeFactor_mass;     // 斜率因子
    double minPeakArea_mass;     // 最小峰面积
    double minPeakHeight_mass;   // 最小峰高
    int folderWidthOfNoise_mass; // SNR噪声窗口倍数

    // 默认构造函数
    PeakFindingParametersMass()
        : useDefault_mass(true), smoothType_mass(0), windowSize_mass(5), gaussianSigma_mass(1.0), polyOrder_mass(2), baselineType_mass(0), alsLambda_mass(1e5), alsP_mass(0.01), alsMaxIter_mass(10),
          noiseWindow_mass(20), minPeakWidth_mass(5), slopeFactor_mass(2.0), minPeakArea_mass(0.0), minPeakHeight_mass(0.0), folderWidthOfNoise_mass(10)
    {
    }

    // 比较操作符
    bool operator!=(const PeakFindingParametersMass &other) const
    {
        return useDefault_mass != other.useDefault_mass || smoothType_mass != other.smoothType_mass || windowSize_mass != other.windowSize_mass ||
               qAbs(gaussianSigma_mass - other.gaussianSigma_mass) > 1e-6 || polyOrder_mass != other.polyOrder_mass || baselineType_mass != other.baselineType_mass ||
               qAbs(alsLambda_mass - other.alsLambda_mass) > 1e-6 || qAbs(alsP_mass - other.alsP_mass) > 1e-6 || alsMaxIter_mass != other.alsMaxIter_mass ||
               noiseWindow_mass != other.noiseWindow_mass || minPeakWidth_mass != other.minPeakWidth_mass || qAbs(slopeFactor_mass - other.slopeFactor_mass) > 1e-6 ||
               qAbs(minPeakArea_mass - other.minPeakArea_mass) > 1e-6 || qAbs(minPeakHeight_mass - other.minPeakHeight_mass) > 1e-6 ||
               folderWidthOfNoise_mass != other.folderWidthOfNoise_mass;
    }
};

/**
 * @brief OptionsDialog配置管理器
 * 统一管理OptionsDialog所有页面的配置参数
 */
class OptionsDialogSettings : public ConfigManager
{
public:
    // 获取单例实例
    static OptionsDialogSettings &getInstance();

    // 访问结构体 - 峰识别与标记页面
    static PeakFindingChromatograms &getChromatogramsSettings();
    static PeakFindingMass &getMassSettings();
    static PeakFindingDAD &getDADSettings();

    // 访问结构体 - 新增页面
    static AppearanceSettings &getAppearanceSettings();
    static PeakFindingParameters &getPeakFindingParametersSettings();
    static PeakFindingParametersMass &getPeakFindingParametersMassSettings();

    // 保存和加载所有配置
    static bool saveToXML();
    static bool loadFromXML();
    bool loadConfig() override;

    // 获取当前谱图线宽
    static double getSpectrumLineWidth();

private:
    // 私有构造函数
    OptionsDialogSettings();

    // 禁止拷贝构造和赋值
    OptionsDialogSettings(const OptionsDialogSettings &) = delete;
    OptionsDialogSettings &operator=(const OptionsDialogSettings &) = delete;

    // 结构体实例 - 原有的峰识别与标记页面
    static PeakFindingChromatograms chromatogramsSettings;
    static PeakFindingMass massSettings;
    static PeakFindingDAD dadSettings;

    // 结构体实例 - 新增页面
    static AppearanceSettings appearanceSettings;
    static PeakFindingParameters peakFindingParametersSettings;
    static PeakFindingParametersMass peakFindingParametersMassSettings;

    // 保存方法 - 原有页面
    static bool savePeakFindingChromatogramsToXml();
    static bool savePeakFindingMassToXml();
    static bool savePeakFindingDADToXml();

    // 保存方法 - 新增页面
    static bool saveAppearanceSettingsToXml();
    static bool savePeakFindingParametersToXml();
    static bool savePeakFindingParametersMassToXml();

    // 加载方法 - 原有页面
    static bool loadPeakFindingChromatogramsFromXml();
    static bool loadPeakFindingMassFromXml();
    static bool loadPeakFindingDADFromXml();

    // 加载方法 - 新增页面
    static bool loadAppearanceSettingsFromXml();
    static bool loadPeakFindingParametersFromXml();
    static bool loadPeakFindingParametersMassFromXml();

    // 创建默认配置文件
    bool createDefaultConfigFile();
};

// 保持向后兼容性的别名
using PeakFindingMarkSettings = OptionsDialogSettings;

#endif // PEAKFINDINGCONFIGS_H
