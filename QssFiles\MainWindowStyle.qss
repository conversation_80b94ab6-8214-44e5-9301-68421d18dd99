/* ========================================
   MainWindow 专用样式文件 - 按钮设计
   ======================================== */

/* ========================================
   全局字体设置
   ======================================== */
* {
  font-family: "Microsoft YaHei UI", "Segoe UI", Arial, sans-serif;
}

/* ========================================
   特定GridLayout内的按钮样式
   使用方法：给包含GridLayout的Widget设置objectName为"gridLayout_top"
   ======================================== */

/*
全局字体
*/

QWidget#gridLayout_top QPushButton {
  background-color: rgb(245, 245, 245);
  border: 1px solid #c0c0c0;
  border-radius: 4px;
  font-family: "Microsoft YaHei UI", "Segoe UI", Arial, sans-serif;
  font-size: 18px;
  color: #333333;
  padding: 5px;
  font-weight: bold;
}

QWidget#gridLayout_top QPushButton:hover {
  border-color: #adadad;
  border-width: 2px;
}

QWidget#gridLayout_top QPushButton:pressed {
  background-color: #d4d4d4;
  border-color: #8c8c8c;
}

QWidget#gridLayout_top QPushButton:disabled {
  background-color: #f5f5f5;
  border-color: #d9d9d9;
  color: #999999;
}

/* ========================================
   主窗口背景样式
   ======================================== */
QMainWindow {
  background-color: #f5f5f5;
}

/* ========================================
   状态栏样式
   ======================================== */
QStatusBar {
  background-color: #ffffff;
  border-top: 1px solid #e0e0e0;
  font-size: 14px;
  color: #666666;
}

/* ========================================
   菜单栏样式
   ======================================== */
QMenuBar {
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
  font-size: 14px;
  font-family: "Microsoft YaHei UI", "Segoe UI", Arial, sans-serif;
}

QMenuBar::item {
  background-color: transparent;
  padding: 6px 12px;
  color: #333333;
}

QMenuBar::item:selected {
  background-color: #e3f2fd;
  color: #1976d2;
}

QMenuBar::item:pressed {
  background-color: #bbdefb;
}

/* ========================================
   工具栏样式
   ======================================== */
QToolBar {
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  font-size: 14px;
}

QToolBar::separator {
  background-color: #e0e0e0;
  width: 1px;
  margin: 4px 2px;
}

/* ========================================
   滚动条样式
   ======================================== */
QScrollBar:vertical {
  background-color: #f0f0f0;
  width: 12px;
  border-radius: 6px;
}

QScrollBar::handle:vertical {
  background-color: #c0c0c0;
  border-radius: 6px;
  min-height: 20px;
}

QScrollBar::handle:vertical:hover {
  background-color: #a0a0a0;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
  border: none;
  background: none;
}

QScrollBar:horizontal {
  background-color: #f0f0f0;
  height: 12px;
  border-radius: 6px;
}

QScrollBar::handle:horizontal {
  background-color: #c0c0c0;
  border-radius: 6px;
  min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
  background-color: #a0a0a0;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
  border: none;
  background: none;
}

/* ========================================
   滑块样式 - 灰色圆点设计
   ======================================== */
QSlider::groove:vertical {
  background: #e0e0e0;
  width: 4px;
  border-radius: 2px;
}

QSlider::handle:vertical {
  background: #808080;
  border: 2px solid #606060;
  width: 16px;
  height: 16px;
  border-radius: 10px;
  margin: 0 -8px; /* 让圆点居中对齐滑槽 */
}

QSlider::handle:vertical:hover {
  background: #707070;
  border: 2px solid #505050;
}

QSlider::handle:vertical:pressed {
  background: #606060;
  border: 2px solid #404040;
}

QSlider::add-page:vertical {
  background: #e0e0e0;
  border-radius: 2px;
}

QSlider::sub-page:vertical {
  background: #b0b0b0;
  border-radius: 2px;
}

/* ========================================
   工具提示样式
   ======================================== */
QToolTip {
  background-color: #2b2b2b;
  color: #ffffff;
  border: 1px solid #555555;
  font-size: 18px; /* 增大字体 */
  font-family: "Microsoft YaHei UI", "Segoe UI", Arial, sans-serif;
  font-weight: normal;
}
