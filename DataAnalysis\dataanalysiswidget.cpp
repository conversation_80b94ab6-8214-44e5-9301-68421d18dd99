#include "dataanalysiswidget.h"
#include <QDebug>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QSplitter>
#include <QHeaderView>

DataAnalysisWidget::DataAnalysisWidget(QWidget* pParentToolBar, QWidget *parent)
    : QMainWindow(parent)
    , ui(nullptr)
    , mChartMass(nullptr)
    , mChartTIC(nullptr)
    , fileDataManager(nullptr)
    , dataReader(nullptr)
    , mAnalyzeThread(nullptr)
    , m_threadType(TYPE_TIC)
    , isRestart(true)
    , mCurrentPage(0)
{
    // 设置基本UI
    setupUI();
    
    // 创建数据管理器
    fileDataManager = new FileDataManager(this);
    dataReader = new DataReader(this);
    
    // 创建工作线程
    mAnalyzeThread = new QThread(this);
    
    // 连接信号槽
    connectSignals();
    
    qDebug() << "DataAnalysisWidget: 构造完成";
}

DataAnalysisWidget::~DataAnalysisWidget()
{
    // 停止并等待线程结束
    if (mAnalyzeThread && mAnalyzeThread->isRunning()) {
        mAnalyzeThread->quit();
        mAnalyzeThread->wait(3000); // 等待最多3秒
    }
    
    // 清理资源
    if (mChartTIC) {
        delete mChartTIC;
        mChartTIC = nullptr;
    }
    
    if (mChartMass) {
        delete mChartMass;
        mChartMass = nullptr;
    }
    
    qDebug() << "DataAnalysisWidget: 析构完成";
}

void DataAnalysisWidget::setupUI()
{
    // 创建中央窗口部件
    QWidget *centralWidget = new QWidget(this);
    setCentralWidget(centralWidget);
    
    // 创建主布局
    QVBoxLayout *mainLayout = new QVBoxLayout(centralWidget);
    
    // 创建分割器
    QSplitter *splitter = new QSplitter(Qt::Horizontal, this);
    splitter->setHandleWidth(12);
    splitter->setOpaqueResize(false);
    
    // 创建左侧面板（文件树等）
    QWidget *leftPanel = new QWidget();
    leftPanel->setMinimumWidth(200);
    leftPanel->setMaximumWidth(300);
    
    // 创建右侧面板（图表区域）
    QWidget *rightPanel = new QWidget();
    
    // 创建TIC/XIC图表
    mChartTIC = new LxTicXicChart(this);
    mChartTIC->enablePointSelection(true);
    mChartTIC->setInteractionMode(GlobalEnums::InteractionMode::Mode_Select);
    
    // 创建MASS图表
    mChartMass = new LxMassChart(GlobalEnums::TrackType::MS, this);
    
    // 设置右侧面板布局
    QVBoxLayout *rightLayout = new QVBoxLayout(rightPanel);
    rightLayout->addWidget(mChartTIC, 1);
    rightLayout->addWidget(mChartMass, 1);
    
    // 添加到分割器
    splitter->addWidget(leftPanel);
    splitter->addWidget(rightPanel);
    
    // 设置分割器比例
    QList<int> sizes;
    sizes << 200 << 800;
    splitter->setSizes(sizes);
    
    // 添加到主布局
    mainLayout->addWidget(splitter);
    
    // 设置窗口属性
    setWindowTitle("数据分析");
    resize(1000, 600);
}

void DataAnalysisWidget::connectSignals()
{
    // 连接TIC图表信号
    if (mChartTIC) {
        connect(mChartTIC, &LxChart::sg_showMassChart, this, [this](const QPointF &point, int eventId) {
            onSelectedTIC(point.x());
        });
    }
    
    // 连接内部信号
    connect(this, &DataAnalysisWidget::sUpdateGraphTIC, this, &DataAnalysisWidget::onUpdateGraphTIC, Qt::QueuedConnection);
    connect(this, &DataAnalysisWidget::sUpdateGraphMass, this, &DataAnalysisWidget::onUpdateGraphMass, Qt::QueuedConnection);
    connect(this, &DataAnalysisWidget::sUpdatePageTIC, this, &DataAnalysisWidget::onUpdatePageTIC, Qt::QueuedConnection);
}

void DataAnalysisWidget::initialize()
{
    qDebug() << "DataAnalysisWidget: 初始化完成";
}

bool DataAnalysisWidget::showDataTIC(QString fileName)
{
    if (mAnalyzeThread && mAnalyzeThread->isRunning()) {
        qDebug() << "DataAnalysisWidget: 线程正在运行，无法加载新文件";
        return false;
    }
    
    // 清空现有数据
    mFileNameTIC.clear();
    
    if (!fileName.isEmpty()) {
        mFileNameTIC << fileName;
        m_threadType = TYPE_TIC;
        
        // 启动数据加载线程
        QTimer::singleShot(0, this, [this]() {
            loadFileThreadTIC();
        });
        
        isRestart = true;
        qDebug() << "DataAnalysisWidget: 开始加载TIC数据:" << fileName;
        return true;
    }
    
    return false;
}

bool DataAnalysisWidget::showDataTIC(qint64 page)
{
    if (mAnalyzeThread && mAnalyzeThread->isRunning()) {
        return false;
    }
    
    mCurrentPage = page;
    m_threadType = TYPE_PAGE_TIC;
    
    // 启动分页数据加载
    QTimer::singleShot(0, this, [this]() {
        loadFileThreadPageTIC();
    });
    
    return true;
}

bool DataAnalysisWidget::showDataMass(ThreadType threadType)
{
    if (mAnalyzeThread && mAnalyzeThread->isRunning()) {
        return false;
    }
    
    m_threadType = threadType;
    
    // 启动MASS数据加载
    QTimer::singleShot(0, this, [this]() {
        if (m_threadType == TYPE_MASS) {
            loadFileThreadMass();
        } else if (m_threadType == TYPE_MASS_RANGE) {
            loadFileThreadMassRange();
        }
    });
    
    return true;
}

void DataAnalysisWidget::onSelectedTIC(const double x)
{
    qDebug() << "DataAnalysisWidget: TIC选择点:" << x;
    
    // 查找对应的帧索引
    int listTIC_X = mDataTIC_X.size();
    if (listTIC_X < 1 || mDataTIC_Y.size() != listTIC_X) {
        return;
    }
    
    if (mGraphBuffMutexTIC.tryLock(50)) {
        for (int indexTIC = 0; indexTIC < listTIC_X; ++indexTIC) {
            std::vector<double>& pDataTIC_X = mDataTIC_X[indexTIC];
            int sizeTIC_X = pDataTIC_X.size() - 1;
            
            for (int i = 0; i < sizeTIC_X; ++i) {
                if ((x - pDataTIC_X[i] >= 0.0000001) && (pDataTIC_X[i+1] - x >= 0.0000001)) {
                    if (mFrame.size() > indexTIC && mFrame[indexTIC] != i) {
                        mFrame[indexTIC] = i;
                    }
                    break;
                }
            }
        }
        
        // 加载对应的MASS数据
        showDataMass(TYPE_MASS);
        mGraphBuffMutexTIC.unlock();
    }
}

void DataAnalysisWidget::onUpdateGraphTIC(bool deletePlots)
{
    qDebug() << "DataAnalysisWidget: 更新TIC图表";
    // TODO: 实现TIC图表更新逻辑
}

void DataAnalysisWidget::onUpdateGraphMass(int eventCount)
{
    qDebug() << "DataAnalysisWidget: 更新MASS图表，事件数:" << eventCount;
    // TODO: 实现MASS图表更新逻辑
}

void DataAnalysisWidget::onUpdatePageTIC(qint64 pages)
{
    qDebug() << "DataAnalysisWidget: 更新TIC分页，总页数:" << pages;
    // TODO: 实现分页控件更新逻辑
}

void DataAnalysisWidget::on_UI_PB_LOAD_clicked()
{
    QString fileName = QFileDialog::getOpenFileName(this,
                                                    tr("打开文件"),
                                                    QCoreApplication::applicationDirPath() + "/data",
                                                    "TIC文件 (*.Param);;临时文件 (*.P)",
                                                    nullptr);
    if (!fileName.isNull()) {
        showDataTIC(fileName);
    }
}
