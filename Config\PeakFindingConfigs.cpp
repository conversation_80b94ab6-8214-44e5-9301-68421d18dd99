#include "PeakFindingConfigs.h"

// OptionsDialogSettings静态成员变量初始化
PeakFindingChromatograms OptionsDialogSettings::chromatogramsSettings;
PeakFindingMass OptionsDialogSettings::massSettings;
PeakFindingDAD OptionsDialogSettings::dadSettings;
AppearanceSettings OptionsDialogSettings::appearanceSettings;
PeakFindingParameters OptionsDialogSettings::peakFindingParametersSettings;
PeakFindingParametersMass OptionsDialogSettings::peakFindingParametersMassSettings;

OptionsDialogSettings::OptionsDialogSettings()
{
    // 私有构造函数
}

OptionsDialogSettings &OptionsDialogSettings::getInstance()
{
    static OptionsDialogSettings instance; // 饿汉模式
    return instance;
}

// 峰识别与标记页面访问方法
PeakFindingChromatograms &OptionsDialogSettings::getChromatogramsSettings()
{
    return chromatogramsSettings;
}

PeakFindingMass &OptionsDialogSettings::getMassSettings()
{
    return massSettings;
}

PeakFindingDAD &OptionsDialogSettings::getDADSettings()
{
    return dadSettings;
}

// 新增页面访问方法
AppearanceSettings &OptionsDialogSettings::getAppearanceSettings()
{
    return appearanceSettings;
}

PeakFindingParameters &OptionsDialogSettings::getPeakFindingParametersSettings()
{
    return peakFindingParametersSettings;
}

PeakFindingParametersMass &OptionsDialogSettings::getPeakFindingParametersMassSettings()
{
    return peakFindingParametersMassSettings;
}

bool OptionsDialogSettings::saveToXML()
{
    qDebug() << "OptionsDialogSettings::saveToXML: 开始保存所有OptionsDialog配置到XML";

    bool success = true;

    // 保存峰识别与标记页面配置
    success &= savePeakFindingChromatogramsToXml();
    success &= savePeakFindingMassToXml();
    success &= savePeakFindingDADToXml();

    // 保存新增页面配置
    success &= saveAppearanceSettingsToXml();
    success &= savePeakFindingParametersToXml();
    success &= savePeakFindingParametersMassToXml();

    qDebug() << "OptionsDialogSettings::saveToXML: 保存" << (success ? "成功" : "失败");
    return success;
}

bool OptionsDialogSettings::loadFromXML()
{
    qDebug() << "OptionsDialogSettings::loadFromXML: 开始加载所有OptionsDialog配置从XML";

    bool success = true;

    // 加载峰识别与标记页面配置
    success &= loadPeakFindingChromatogramsFromXml();
    success &= loadPeakFindingMassFromXml();
    success &= loadPeakFindingDADFromXml();

    // 加载新增页面配置
    success &= loadAppearanceSettingsFromXml();
    success &= loadPeakFindingParametersFromXml();
    success &= loadPeakFindingParametersMassFromXml();

    qDebug() << "OptionsDialogSettings::loadFromXML: 加载" << (success ? "成功" : "失败");
    return success;
}

bool OptionsDialogSettings::loadConfig()
{
    qDebug() << "OptionsDialogSettings::loadConfig: 检查配置文件：" << m_filePath;

    // 检查文件是否存在
    QFile file(m_filePath);
    if (!file.exists())
    {
        qDebug() << "OptionsDialogSettings::loadConfig: 配置文件不存在，创建默认配置文件";
        return createDefaultConfigFile();
    }

    // 文件存在，尝试打开
    if (!file.open(QIODevice::ReadOnly))
    {
        qDebug() << "OptionsDialogSettings::loadConfig: 无法打开配置文件" << m_filePath;
        qDebug() << "OptionsDialogSettings::loadConfig: 尝试创建新的默认配置文件";
        return createDefaultConfigFile();
    }

    // 检查文件是否为空
    if (file.size() == 0)
    {
        qDebug() << "OptionsDialogSettings::loadConfig: 配置文件为空，重新创建默认配置";
        file.close();
        return createDefaultConfigFile();
    }

    // 尝试解析XML
    QString errorMsg;
    int errorLine, errorColumn;
    if (!m_doc.setContent(&file, &errorMsg, &errorLine, &errorColumn))
    {
        qDebug() << "OptionsDialogSettings::loadConfig: 无法解析配置文件:" << errorMsg << "在行:" << errorLine << "列:" << errorColumn;
        file.close();
        qDebug() << "OptionsDialogSettings::loadConfig: 配置文件损坏，重新创建默认配置";
        return createDefaultConfigFile();
    }

    file.close();
    qDebug() << "OptionsDialogSettings::loadConfig: 配置文件加载成功，开始读取配置参数";

    // 加载所有配置参数
    bool loadSuccess = loadFromXML();
    if (!loadSuccess)
    {
        qDebug() << "OptionsDialogSettings::loadConfig: 配置参数加载失败，使用默认参数";
        // 即使加载失败，也返回true，使用默认构造函数的值
    }

    return true;
}

bool OptionsDialogSettings::createDefaultConfigFile()
{
    qDebug() << "OptionsDialogSettings::createDefaultConfigFile: 创建默认配置文件：" << m_filePath;

    // 确保目录存在
    QFileInfo fileInfo(m_filePath);
    QDir dir = fileInfo.absoluteDir();
    if (!dir.exists())
    {
        if (!dir.mkpath("."))
        {
            qDebug() << "OptionsDialogSettings::createDefaultConfigFile: 无法创建目录：" << dir.absolutePath();
            return false;
        }
        qDebug() << "OptionsDialogSettings::createDefaultConfigFile: 创建目录：" << dir.absolutePath();
    }

    // 创建默认的 XML 内容
    QString defaultConfig = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n"
                            "<root software=\"HZH_OS\" version=\"1.0\">\n"
                            "</root>\n";

    // 创建文件并写入默认内容
    QFile file(m_filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Truncate))
    {
        qDebug() << "OptionsDialogSettings::createDefaultConfigFile: 无法创建配置文件：" << m_filePath;
        return false;
    }

    QTextStream stream(&file);
    stream << defaultConfig;
    file.close();

    // 解析刚创建的XML
    if (!m_doc.setContent(defaultConfig))
    {
        qDebug() << "OptionsDialogSettings::createDefaultConfigFile: 无法解析默认XML内容";
        return false;
    }

    // 使用默认参数初始化所有配置结构体（已在构造函数中完成）
    qDebug() << "OptionsDialogSettings::createDefaultConfigFile: 使用原始UI参数作为默认值";

    // 保存默认配置到XML文件
    if (saveToXML())
    {
        qDebug() << "OptionsDialogSettings::createDefaultConfigFile: 默认配置已保存到XML文件";
        isFirstInit = true;
        return true;
    }
    else
    {
        qDebug() << "OptionsDialogSettings::createDefaultConfigFile: 保存默认配置失败";
        return false;
    }
}

// 移除旧的构造函数，使用OptionsDialogSettings的构造函数

bool OptionsDialogSettings::savePeakFindingChromatogramsToXml()
{
    setProperty("PeakFindingChromatograms", "FiledType", QString::number(to_uint(chromatogramsSettings.FiledType)));
    setProperty("PeakFindingChromatograms", "PeakThreshold", QString::number(chromatogramsSettings.PeakThreshold));
    setProperty("PeakFindingChromatograms", "NoisePercent", QString::number(chromatogramsSettings.NoisePercent));
    setProperty("PeakFindingChromatograms", "PeakSplit", QString::number(chromatogramsSettings.PeakSplit));
    setProperty("PeakFindingChromatograms", "Fitting", QString::number(chromatogramsSettings.Fitting));
    setProperty("PeakFindingChromatograms", "Diff", QString::number(chromatogramsSettings.Diff));
    return true;
}

bool OptionsDialogSettings::savePeakFindingMassToXml()
{
    setProperty("PeakFindingMass", "FiledType", QString::number(to_uint(massSettings.FiledType)));
    setProperty("PeakFindingMass", "PeakThreshold", QString::number(massSettings.PeakThreshold));
    setProperty("PeakFindingMass", "CentroidHeightPercentage", QString::number(massSettings.CentroidHeightPercentage));
    return true;
}

bool OptionsDialogSettings::savePeakFindingDADToXml()
{
    setProperty("PeakFindingDAD", "FiledType", QString::number(to_uint(dadSettings.FiledType)));
    setProperty("PeakFindingDAD", "PeakThreshold", QString::number(dadSettings.PeakThreshold));
    setProperty("PeakFindingDAD", "NoisePercent", QString::number(dadSettings.NoisePercent));
    setProperty("PeakFindingDAD", "PeakSplit", QString::number(dadSettings.PeakSplit));
    setProperty("PeakFindingDAD", "Fitting", QString::number(dadSettings.Fitting));
    setProperty("PeakFindingDAD", "Diff", QString::number(dadSettings.Diff));
    return true;
}

bool OptionsDialogSettings::loadPeakFindingChromatogramsFromXml()
{
    LabelFiledType type;
    QString FiledTypeStr = getPropertyValue("PeakFindingChromatograms", "FiledType");
    QString PeakThresholdStr = getPropertyValue("PeakFindingChromatograms", "PeakThreshold");
    QString NoisePercentStr = getPropertyValue("PeakFindingChromatograms", "NoisePercent");
    QString PeakSplitStr = getPropertyValue("PeakFindingChromatograms", "PeakSplit");
    QString FittingStr = getPropertyValue("PeakFindingChromatograms", "Fitting");
    QString DiffStr = getPropertyValue("PeakFindingChromatograms", "Diff");
    if (FiledTypeStr.isEmpty() || PeakThresholdStr.isEmpty() || NoisePercentStr.isEmpty() || PeakSplitStr.isEmpty() || FittingStr.isEmpty() ||
        DiffStr.isEmpty())
    {
        return false;
    }
    type = from_uint(FiledTypeStr.toUInt());
    getChromatogramsSettings().FiledType = type;
    getChromatogramsSettings().PeakThreshold = PeakThresholdStr.toUInt();
    getChromatogramsSettings().NoisePercent = NoisePercentStr.toUInt();
    getChromatogramsSettings().PeakSplit = PeakSplitStr.toUInt();
    getChromatogramsSettings().Fitting = FittingStr.toUInt();
    getChromatogramsSettings().Diff = DiffStr.toUInt();

    return true;
}

bool OptionsDialogSettings::loadPeakFindingMassFromXml()
{
    LabelFiledType type;
    QString FiledTypeStr = getPropertyValue("PeakFindingMass", "FiledType");
    QString PeakThresholdStr = getPropertyValue("PeakFindingMass", "PeakThreshold");
    QString CentroidHeightPercentageStr = getPropertyValue("PeakFindingMass", "CentroidHeightPercentage");
    if (FiledTypeStr.isEmpty() || PeakThresholdStr.isEmpty() || CentroidHeightPercentageStr.isEmpty())
    {
        return false;
    }
    type = from_uint(FiledTypeStr.toUInt());
    getMassSettings().FiledType = type;
    getMassSettings().PeakThreshold = PeakThresholdStr.toUInt();
    getMassSettings().CentroidHeightPercentage = CentroidHeightPercentageStr.toUInt();
    return true;
}

bool OptionsDialogSettings::loadPeakFindingDADFromXml()
{
    LabelFiledType type;
    QString FiledTypeStr = getPropertyValue("PeakFindingDAD", "FiledType");
    QString PeakThresholdStr = getPropertyValue("PeakFindingDAD", "PeakThreshold");
    QString NoisePercentStr = getPropertyValue("PeakFindingDAD", "NoisePercent");
    QString PeakSplitStr = getPropertyValue("PeakFindingDAD", "PeakSplit");
    QString FittingStr = getPropertyValue("PeakFindingDAD", "Fitting");
    QString DiffStr = getPropertyValue("PeakFindingDAD", "Diff");
    if (FiledTypeStr.isEmpty() || PeakThresholdStr.isEmpty() || NoisePercentStr.isEmpty() || PeakSplitStr.isEmpty() || FittingStr.isEmpty() ||
        DiffStr.isEmpty())
    {
        return false;
    }
    type = from_uint(FiledTypeStr.toUInt());
    getDADSettings().FiledType = type;
    getDADSettings().PeakThreshold = PeakThresholdStr.toUInt();
    getDADSettings().NoisePercent = NoisePercentStr.toUInt();
    getDADSettings().PeakSplit = PeakSplitStr.toUInt();
    getDADSettings().Fitting = FittingStr.toUInt();
    getDADSettings().Diff = DiffStr.toUInt();

    return true;
}

// ==================== 新增页面保存方法 ====================

bool OptionsDialogSettings::saveAppearanceSettingsToXml()
{
    qDebug() << "OptionsDialogSettings::saveAppearanceSettingsToXml: 保存外观设置";

    const AppearanceSettings &settings = getAppearanceSettings();

    // 保存图层标题字体设置
    setProperty("AppearanceSettings", "axisLabelFontFamily", settings.axisLabelFontFamily);
    setProperty("AppearanceSettings", "axisLabelFontSize", QString::number(settings.axisLabelFontSize));
    setProperty("AppearanceSettings", "axisLabelFontBold", settings.axisLabelFontBold ? "true" : "false");
    setProperty("AppearanceSettings", "axisLabelFontItalic", settings.axisLabelFontItalic ? "true" : "false");
    setProperty("AppearanceSettings", "axisLabelFontStrikeOut", settings.axisLabelFontStrikeOut ? "true" : "false");
    setProperty("AppearanceSettings", "axisLabelFontUnderline", settings.axisLabelFontUnderline ? "true" : "false");

    // 保存坐标轴标题字体设置
    setProperty("AppearanceSettings", "autoLabelFontFamily", settings.autoLabelFontFamily);
    setProperty("AppearanceSettings", "autoLabelFontSize", QString::number(settings.autoLabelFontSize));
    setProperty("AppearanceSettings", "autoLabelFontBold", settings.autoLabelFontBold ? "true" : "false");
    setProperty("AppearanceSettings", "autoLabelFontItalic", settings.autoLabelFontItalic ? "true" : "false");
    setProperty("AppearanceSettings", "autoLabelFontStrikeOut", settings.autoLabelFontStrikeOut ? "true" : "false");
    setProperty("AppearanceSettings", "autoLabelFontUnderline", settings.autoLabelFontUnderline ? "true" : "false");

    // 保存坐标轴标签字体设置
    setProperty("AppearanceSettings", "customLabelFontFamily", settings.customLabelFontFamily);
    setProperty("AppearanceSettings", "customLabelFontSize", QString::number(settings.customLabelFontSize));
    setProperty("AppearanceSettings", "customLabelFontBold", settings.customLabelFontBold ? "true" : "false");
    setProperty("AppearanceSettings", "customLabelFontItalic", settings.customLabelFontItalic ? "true" : "false");
    setProperty("AppearanceSettings", "customLabelFontStrikeOut", settings.customLabelFontStrikeOut ? "true" : "false");
    setProperty("AppearanceSettings", "customLabelFontUnderline", settings.customLabelFontUnderline ? "true" : "false");

    // 保存自动标题字体设置
    setProperty("AppearanceSettings", "axisTitleFontFamily", settings.axisTitleFontFamily);
    setProperty("AppearanceSettings", "axisTitleFontSize", QString::number(settings.axisTitleFontSize));
    setProperty("AppearanceSettings", "axisTitleFontBold", settings.axisTitleFontBold ? "true" : "false");
    setProperty("AppearanceSettings", "axisTitleFontItalic", settings.axisTitleFontItalic ? "true" : "false");
    setProperty("AppearanceSettings", "axisTitleFontStrikeOut", settings.axisTitleFontStrikeOut ? "true" : "false");
    setProperty("AppearanceSettings", "axisTitleFontUnderline", settings.axisTitleFontUnderline ? "true" : "false");

    // 保存手动标题字体设置
    setProperty("AppearanceSettings", "titleFontFamily", settings.titleFontFamily);
    setProperty("AppearanceSettings", "titleFontSize", QString::number(settings.titleFontSize));
    setProperty("AppearanceSettings", "titleFontBold", settings.titleFontBold ? "true" : "false");
    setProperty("AppearanceSettings", "titleFontItalic", settings.titleFontItalic ? "true" : "false");
    setProperty("AppearanceSettings", "titleFontStrikeOut", settings.titleFontStrikeOut ? "true" : "false");
    setProperty("AppearanceSettings", "titleFontUnderline", settings.titleFontUnderline ? "true" : "false");

    // 保存谱图线宽设置
    setProperty("AppearanceSettings", "spectrumLineWidth", QString::number(settings.spectrumLineWidth));

    qDebug() << "OptionsDialogSettings::saveAppearanceSettingsToXml: 外观设置保存完成";

    return true;
}

bool OptionsDialogSettings::savePeakFindingParametersToXml()
{
    qDebug() << "OptionsDialogSettings::savePeakFindingParametersToXml: 保存寻峰参数";

    const PeakFindingParameters &params = getPeakFindingParametersSettings();

    // 保存使用默认参数标志
    setProperty("PeakFindingParameters", "useDefault", params.useDefault ? "true" : "false");

    // 保存平滑参数
    setProperty("PeakFindingParameters", "smoothType", QString::number(params.smoothType));
    setProperty("PeakFindingParameters", "windowSize", QString::number(params.windowSize));
    setProperty("PeakFindingParameters", "gaussianSigma", QString::number(params.gaussianSigma));
    setProperty("PeakFindingParameters", "polyOrder", QString::number(params.polyOrder));

    // 保存基线校正参数
    setProperty("PeakFindingParameters", "baselineType", QString::number(params.baselineType));
    setProperty("PeakFindingParameters", "alsLambda", QString::number(params.alsLambda, 'e', 0));
    setProperty("PeakFindingParameters", "alsP", QString::number(params.alsP));
    setProperty("PeakFindingParameters", "alsMaxIter", QString::number(params.alsMaxIter));

    // 保存寻峰参数
    setProperty("PeakFindingParameters", "noiseWindow", QString::number(params.noiseWindow));
    setProperty("PeakFindingParameters", "minPeakWidth", QString::number(params.minPeakWidth));
    setProperty("PeakFindingParameters", "slopeFactor", QString::number(params.slopeFactor));
    setProperty("PeakFindingParameters", "minPeakArea", QString::number(params.minPeakArea));
    setProperty("PeakFindingParameters", "minPeakHeight", QString::number(params.minPeakHeight));
    setProperty("PeakFindingParameters", "folderWidthOfNoise", QString::number(params.folderWidthOfNoise));

    qDebug() << "OptionsDialogSettings::savePeakFindingParametersToXml: 寻峰参数保存完成";

    return true;
}

// ==================== 新增页面加载方法 ====================

bool OptionsDialogSettings::loadAppearanceSettingsFromXml()
{
    qDebug() << "OptionsDialogSettings::loadAppearanceSettingsFromXml: 加载外观设置";

    AppearanceSettings &settings = getAppearanceSettings();

    // 加载旧的通用字体设置（向后兼容）
    QString fontFamily = getPropertyValue("AppearanceSettings", "fontFamily");
    QString fontSize = getPropertyValue("AppearanceSettings", "fontSize");
    QString fontBold = getPropertyValue("AppearanceSettings", "fontBold");
    QString fontItalic = getPropertyValue("AppearanceSettings", "fontItalic");

    // 加载新的具体字体设置
    QString axisLabelFontFamily = getPropertyValue("AppearanceSettings", "axisLabelFontFamily");
    QString axisLabelFontSize = getPropertyValue("AppearanceSettings", "axisLabelFontSize");
    QString axisLabelFontBold = getPropertyValue("AppearanceSettings", "axisLabelFontBold");
    QString axisLabelFontItalic = getPropertyValue("AppearanceSettings", "axisLabelFontItalic");
    QString axisLabelFontStrikeOut = getPropertyValue("AppearanceSettings", "axisLabelFontStrikeOut");
    QString axisLabelFontUnderline = getPropertyValue("AppearanceSettings", "axisLabelFontUnderline");

    QString autoLabelFontFamily = getPropertyValue("AppearanceSettings", "autoLabelFontFamily");
    QString autoLabelFontSize = getPropertyValue("AppearanceSettings", "autoLabelFontSize");
    QString autoLabelFontBold = getPropertyValue("AppearanceSettings", "autoLabelFontBold");
    QString autoLabelFontItalic = getPropertyValue("AppearanceSettings", "autoLabelFontItalic");
    QString autoLabelFontStrikeOut = getPropertyValue("AppearanceSettings", "autoLabelFontStrikeOut");
    QString autoLabelFontUnderline = getPropertyValue("AppearanceSettings", "autoLabelFontUnderline");

    QString customLabelFontFamily = getPropertyValue("AppearanceSettings", "customLabelFontFamily");
    QString customLabelFontSize = getPropertyValue("AppearanceSettings", "customLabelFontSize");
    QString customLabelFontBold = getPropertyValue("AppearanceSettings", "customLabelFontBold");
    QString customLabelFontItalic = getPropertyValue("AppearanceSettings", "customLabelFontItalic");
    QString customLabelFontStrikeOut = getPropertyValue("AppearanceSettings", "customLabelFontStrikeOut");
    QString customLabelFontUnderline = getPropertyValue("AppearanceSettings", "customLabelFontUnderline");

    QString axisTitleFontFamily = getPropertyValue("AppearanceSettings", "axisTitleFontFamily");
    QString axisTitleFontSize = getPropertyValue("AppearanceSettings", "axisTitleFontSize");
    QString axisTitleFontBold = getPropertyValue("AppearanceSettings", "axisTitleFontBold");
    QString axisTitleFontItalic = getPropertyValue("AppearanceSettings", "axisTitleFontItalic");
    QString axisTitleFontStrikeOut = getPropertyValue("AppearanceSettings", "axisTitleFontStrikeOut");
    QString axisTitleFontUnderline = getPropertyValue("AppearanceSettings", "axisTitleFontUnderline");

    QString titleFontFamily = getPropertyValue("AppearanceSettings", "titleFontFamily");
    QString titleFontSize = getPropertyValue("AppearanceSettings", "titleFontSize");
    QString titleFontBold = getPropertyValue("AppearanceSettings", "titleFontBold");
    QString titleFontItalic = getPropertyValue("AppearanceSettings", "titleFontItalic");
    QString titleFontStrikeOut = getPropertyValue("AppearanceSettings", "titleFontStrikeOut");
    QString titleFontUnderline = getPropertyValue("AppearanceSettings", "titleFontUnderline");

    // 加载谱图线宽设置
    QString spectrumLineWidth = getPropertyValue("AppearanceSettings", "spectrumLineWidth");

    // 向后兼容：如果存在旧的线宽字段，使用它们
    QString defaultLineWidth = getPropertyValue("AppearanceSettings", "defaultLineWidth");

    // 如果关键值都为空，说明是第一次运行，使用默认值
    if (fontFamily.isEmpty() && axisLabelFontFamily.isEmpty() && spectrumLineWidth.isEmpty() && defaultLineWidth.isEmpty())
    {
        qDebug() << "OptionsDialogSettings::loadAppearanceSettingsFromXml: 使用默认外观设置";
        return true; // 使用默认构造函数的值
    }

    // 为了向后兼容，如果存在旧的字段，将其应用到所有字体设置
    if (!fontFamily.isEmpty())
    {
        settings.axisLabelFontFamily = fontFamily;
        settings.autoLabelFontFamily = fontFamily;
        settings.customLabelFontFamily = fontFamily;
        settings.axisTitleFontFamily = fontFamily;
        settings.titleFontFamily = fontFamily;
    }
    if (!fontSize.isEmpty())
    {
        int size = fontSize.toInt();
        settings.axisLabelFontSize = size;
        settings.autoLabelFontSize = size;
        settings.customLabelFontSize = size;
        settings.axisTitleFontSize = size;
        settings.titleFontSize = size;
    }
    if (!fontBold.isEmpty())
    {
        bool bold = (fontBold == "true");
        settings.axisLabelFontBold = bold;
        settings.autoLabelFontBold = bold;
        settings.customLabelFontBold = bold;
        settings.axisTitleFontBold = bold;
        settings.titleFontBold = bold;
    }
    if (!fontItalic.isEmpty())
    {
        bool italic = (fontItalic == "true");
        settings.axisLabelFontItalic = italic;
        settings.autoLabelFontItalic = italic;
        settings.customLabelFontItalic = italic;
        settings.axisTitleFontItalic = italic;
        settings.titleFontItalic = italic;
    }

    // 加载新的具体字体设置（优先级高于向后兼容设置）
    if (!axisLabelFontFamily.isEmpty())
        settings.axisLabelFontFamily = axisLabelFontFamily;
    if (!axisLabelFontSize.isEmpty())
        settings.axisLabelFontSize = axisLabelFontSize.toInt();
    if (!axisLabelFontBold.isEmpty())
        settings.axisLabelFontBold = (axisLabelFontBold == "true");
    if (!axisLabelFontItalic.isEmpty())
        settings.axisLabelFontItalic = (axisLabelFontItalic == "true");

    if (!autoLabelFontFamily.isEmpty())
        settings.autoLabelFontFamily = autoLabelFontFamily;
    if (!autoLabelFontSize.isEmpty())
        settings.autoLabelFontSize = autoLabelFontSize.toInt();
    if (!autoLabelFontBold.isEmpty())
        settings.autoLabelFontBold = (autoLabelFontBold == "true");
    if (!autoLabelFontItalic.isEmpty())
        settings.autoLabelFontItalic = (autoLabelFontItalic == "true");

    if (!customLabelFontFamily.isEmpty())
        settings.customLabelFontFamily = customLabelFontFamily;
    if (!customLabelFontSize.isEmpty())
        settings.customLabelFontSize = customLabelFontSize.toInt();
    if (!customLabelFontBold.isEmpty())
        settings.customLabelFontBold = (customLabelFontBold == "true");
    if (!customLabelFontItalic.isEmpty())
        settings.customLabelFontItalic = (customLabelFontItalic == "true");

    if (!axisTitleFontFamily.isEmpty())
        settings.axisTitleFontFamily = axisTitleFontFamily;
    if (!axisTitleFontSize.isEmpty())
        settings.axisTitleFontSize = axisTitleFontSize.toInt();
    if (!axisTitleFontBold.isEmpty())
        settings.axisTitleFontBold = (axisTitleFontBold == "true");
    if (!axisTitleFontItalic.isEmpty())
        settings.axisTitleFontItalic = (axisTitleFontItalic == "true");

    if (!titleFontFamily.isEmpty())
        settings.titleFontFamily = titleFontFamily;
    if (!titleFontSize.isEmpty())
        settings.titleFontSize = titleFontSize.toInt();
    if (!titleFontBold.isEmpty())
        settings.titleFontBold = (titleFontBold == "true");
    if (!titleFontItalic.isEmpty())
        settings.titleFontItalic = (titleFontItalic == "true");
    if (!titleFontStrikeOut.isEmpty())
        settings.titleFontStrikeOut = (titleFontStrikeOut == "true");
    if (!titleFontUnderline.isEmpty())
        settings.titleFontUnderline = (titleFontUnderline == "true");

    // 加载Effects设置
    if (!axisLabelFontStrikeOut.isEmpty())
        settings.axisLabelFontStrikeOut = (axisLabelFontStrikeOut == "true");
    if (!axisLabelFontUnderline.isEmpty())
        settings.axisLabelFontUnderline = (axisLabelFontUnderline == "true");

    if (!autoLabelFontStrikeOut.isEmpty())
        settings.autoLabelFontStrikeOut = (autoLabelFontStrikeOut == "true");
    if (!autoLabelFontUnderline.isEmpty())
        settings.autoLabelFontUnderline = (autoLabelFontUnderline == "true");

    if (!customLabelFontStrikeOut.isEmpty())
        settings.customLabelFontStrikeOut = (customLabelFontStrikeOut == "true");
    if (!customLabelFontUnderline.isEmpty())
        settings.customLabelFontUnderline = (customLabelFontUnderline == "true");

    if (!axisTitleFontStrikeOut.isEmpty())
        settings.axisTitleFontStrikeOut = (axisTitleFontStrikeOut == "true");
    if (!axisTitleFontUnderline.isEmpty())
        settings.axisTitleFontUnderline = (axisTitleFontUnderline == "true");

    // 加载谱图线宽设置
    if (!spectrumLineWidth.isEmpty())
        settings.spectrumLineWidth = spectrumLineWidth.toDouble();
    else if (!defaultLineWidth.isEmpty()) // 向后兼容
        settings.spectrumLineWidth = defaultLineWidth.toDouble();

    return true;
}

bool OptionsDialogSettings::loadPeakFindingParametersFromXml()
{
    qDebug() << "OptionsDialogSettings::loadPeakFindingParametersFromXml: 加载寻峰参数";

    PeakFindingParameters &params = getPeakFindingParametersSettings();

    qDebug() << "加载前的液相参数值:";
    qDebug() << "  useDefault:" << params.useDefault;
    qDebug() << "  smoothType:" << params.smoothType;
    qDebug() << "  windowSize:" << params.windowSize;
    qDebug() << "  alsLambda:" << params.alsLambda;

    // 加载使用默认参数标志
    QString useDefault = getPropertyValue("PeakFindingParameters", "useDefault");

    // 加载平滑参数
    QString smoothType = getPropertyValue("PeakFindingParameters", "smoothType");
    QString windowSize = getPropertyValue("PeakFindingParameters", "windowSize");
    QString gaussianSigma = getPropertyValue("PeakFindingParameters", "gaussianSigma");
    QString polyOrder = getPropertyValue("PeakFindingParameters", "polyOrder");

    // 加载基线校正参数
    QString baselineType = getPropertyValue("PeakFindingParameters", "baselineType");
    QString alsLambda = getPropertyValue("PeakFindingParameters", "alsLambda");
    QString alsP = getPropertyValue("PeakFindingParameters", "alsP");
    QString alsMaxIter = getPropertyValue("PeakFindingParameters", "alsMaxIter");

    // 加载寻峰参数
    QString noiseWindow = getPropertyValue("PeakFindingParameters", "noiseWindow");
    QString minPeakWidth = getPropertyValue("PeakFindingParameters", "minPeakWidth");
    QString slopeFactor = getPropertyValue("PeakFindingParameters", "slopeFactor");
    QString minPeakArea = getPropertyValue("PeakFindingParameters", "minPeakArea");
    QString minPeakHeight = getPropertyValue("PeakFindingParameters", "minPeakHeight");
    QString folderWidthOfNoise = getPropertyValue("PeakFindingParameters", "folderWidthOfNoise");

    qDebug() << "从XML读取的液相寻峰参数原始值:";
    qDebug() << "  noiseWindow:" << noiseWindow;
    qDebug() << "  minPeakWidth:" << minPeakWidth;
    qDebug() << "  slopeFactor:" << slopeFactor;
    qDebug() << "  minPeakArea:" << minPeakArea;
    qDebug() << "  minPeakHeight:" << minPeakHeight;
    qDebug() << "  folderWidthOfNoise:" << folderWidthOfNoise;

    // 如果关键值为空，说明是第一次运行，使用默认值
    if (useDefault.isEmpty() && smoothType.isEmpty() && noiseWindow.isEmpty())
    {
        qDebug() << "OptionsDialogSettings::loadPeakFindingParametersFromXml: 使用默认寻峰参数";
        return true; // 使用默认构造函数的值
    }

    // 应用加载的值
    if (!useDefault.isEmpty())
        params.useDefault = (useDefault == "true");
    if (!smoothType.isEmpty())
        params.smoothType = smoothType.toInt();
    if (!windowSize.isEmpty())
        params.windowSize = windowSize.toInt();
    if (!gaussianSigma.isEmpty())
        params.gaussianSigma = gaussianSigma.toDouble();
    if (!polyOrder.isEmpty())
        params.polyOrder = polyOrder.toInt();
    if (!baselineType.isEmpty())
        params.baselineType = baselineType.toInt();
    if (!alsLambda.isEmpty())
        params.alsLambda = alsLambda.toDouble();
    if (!alsP.isEmpty())
        params.alsP = alsP.toDouble();
    if (!alsMaxIter.isEmpty())
        params.alsMaxIter = alsMaxIter.toInt();
    if (!noiseWindow.isEmpty())
        params.noiseWindow = noiseWindow.toInt();
    if (!minPeakWidth.isEmpty())
        params.minPeakWidth = minPeakWidth.toInt();
    if (!slopeFactor.isEmpty())
        params.slopeFactor = slopeFactor.toDouble();
    if (!minPeakArea.isEmpty())
        params.minPeakArea = minPeakArea.toDouble();
    if (!minPeakHeight.isEmpty())
        params.minPeakHeight = minPeakHeight.toDouble();
    if (!folderWidthOfNoise.isEmpty())
        params.folderWidthOfNoise = folderWidthOfNoise.toInt();

    qDebug() << "加载后的液相参数值:";
    qDebug() << "  useDefault:" << params.useDefault;
    qDebug() << "  smoothType:" << params.smoothType;
    qDebug() << "  windowSize:" << params.windowSize;
    qDebug() << "  gaussianSigma:" << params.gaussianSigma;
    qDebug() << "  polyOrder:" << params.polyOrder;
    qDebug() << "  baselineType:" << params.baselineType;
    qDebug() << "  alsLambda:" << params.alsLambda;
    qDebug() << "  alsP:" << params.alsP;
    qDebug() << "  alsMaxIter:" << params.alsMaxIter;
    qDebug() << "  noiseWindow:" << params.noiseWindow;
    qDebug() << "  minPeakWidth:" << params.minPeakWidth;
    qDebug() << "  slopeFactor:" << params.slopeFactor;
    qDebug() << "  minPeakArea:" << params.minPeakArea;
    qDebug() << "  minPeakHeight:" << params.minPeakHeight;
    qDebug() << "  folderWidthOfNoise:" << params.folderWidthOfNoise;

    return true;
}

double OptionsDialogSettings::getSpectrumLineWidth()
{
    return getAppearanceSettings().spectrumLineWidth;
}

bool OptionsDialogSettings::savePeakFindingParametersMassToXml()
{
    qDebug() << "OptionsDialogSettings::savePeakFindingParametersMassToXml: 保存质谱寻峰参数";

    const PeakFindingParametersMass &params = getPeakFindingParametersMassSettings();

    // 保存使用默认参数标志
    setProperty("PeakFindingParametersMass", "useDefault_mass", params.useDefault_mass ? "true" : "false");

    // 保存平滑参数
    setProperty("PeakFindingParametersMass", "smoothType_mass", QString::number(params.smoothType_mass));
    setProperty("PeakFindingParametersMass", "windowSize_mass", QString::number(params.windowSize_mass));
    setProperty("PeakFindingParametersMass", "gaussianSigma_mass", QString::number(params.gaussianSigma_mass));
    setProperty("PeakFindingParametersMass", "polyOrder_mass", QString::number(params.polyOrder_mass));

    // 保存基线校正参数
    setProperty("PeakFindingParametersMass", "baselineType_mass", QString::number(params.baselineType_mass));
    setProperty("PeakFindingParametersMass", "alsLambda_mass", QString::number(params.alsLambda_mass, 'e', 0));
    setProperty("PeakFindingParametersMass", "alsP_mass", QString::number(params.alsP_mass));
    setProperty("PeakFindingParametersMass", "alsMaxIter_mass", QString::number(params.alsMaxIter_mass));

    // 保存寻峰参数
    setProperty("PeakFindingParametersMass", "noiseWindow_mass", QString::number(params.noiseWindow_mass));
    setProperty("PeakFindingParametersMass", "minPeakWidth_mass", QString::number(params.minPeakWidth_mass));
    setProperty("PeakFindingParametersMass", "slopeFactor_mass", QString::number(params.slopeFactor_mass));
    setProperty("PeakFindingParametersMass", "minPeakArea_mass", QString::number(params.minPeakArea_mass));
    setProperty("PeakFindingParametersMass", "minPeakHeight_mass", QString::number(params.minPeakHeight_mass));
    setProperty("PeakFindingParametersMass", "folderWidthOfNoise_mass", QString::number(params.folderWidthOfNoise_mass));

    qDebug() << "OptionsDialogSettings::savePeakFindingParametersMassToXml: 质谱寻峰参数保存完成";

    return true;
}

bool OptionsDialogSettings::loadPeakFindingParametersMassFromXml()
{
    qDebug() << "OptionsDialogSettings::loadPeakFindingParametersMassFromXml: 加载质谱寻峰参数";

    PeakFindingParametersMass &params = getPeakFindingParametersMassSettings();

    qDebug() << "加载前的质谱参数值:";
    qDebug() << "  useDefault_mass:" << params.useDefault_mass;
    qDebug() << "  smoothType_mass:" << params.smoothType_mass;
    qDebug() << "  windowSize_mass:" << params.windowSize_mass;
    qDebug() << "  alsLambda_mass:" << params.alsLambda_mass;

    // 加载使用默认参数标志
    QString useDefault_mass = getPropertyValue("PeakFindingParametersMass", "useDefault_mass");
    if (!useDefault_mass.isEmpty())
        params.useDefault_mass = (useDefault_mass == "true");

    // 加载平滑参数
    QString smoothType_mass = getPropertyValue("PeakFindingParametersMass", "smoothType_mass");
    if (!smoothType_mass.isEmpty())
        params.smoothType_mass = smoothType_mass.toInt();

    QString windowSize_mass = getPropertyValue("PeakFindingParametersMass", "windowSize_mass");
    if (!windowSize_mass.isEmpty())
        params.windowSize_mass = windowSize_mass.toInt();

    QString gaussianSigma_mass = getPropertyValue("PeakFindingParametersMass", "gaussianSigma_mass");
    if (!gaussianSigma_mass.isEmpty())
        params.gaussianSigma_mass = gaussianSigma_mass.toDouble();

    QString polyOrder_mass = getPropertyValue("PeakFindingParametersMass", "polyOrder_mass");
    if (!polyOrder_mass.isEmpty())
        params.polyOrder_mass = polyOrder_mass.toInt();

    // 加载基线校正参数
    QString baselineType_mass = getPropertyValue("PeakFindingParametersMass", "baselineType_mass");
    if (!baselineType_mass.isEmpty())
        params.baselineType_mass = baselineType_mass.toInt();

    QString alsLambda_mass = getPropertyValue("PeakFindingParametersMass", "alsLambda_mass");
    if (!alsLambda_mass.isEmpty())
        params.alsLambda_mass = alsLambda_mass.toDouble();

    QString alsP_mass = getPropertyValue("PeakFindingParametersMass", "alsP_mass");
    if (!alsP_mass.isEmpty())
        params.alsP_mass = alsP_mass.toDouble();

    QString alsMaxIter_mass = getPropertyValue("PeakFindingParametersMass", "alsMaxIter_mass");
    if (!alsMaxIter_mass.isEmpty())
        params.alsMaxIter_mass = alsMaxIter_mass.toInt();

    // 加载寻峰参数
    QString noiseWindow_mass = getPropertyValue("PeakFindingParametersMass", "noiseWindow_mass");
    QString minPeakWidth_mass = getPropertyValue("PeakFindingParametersMass", "minPeakWidth_mass");
    QString slopeFactor_mass = getPropertyValue("PeakFindingParametersMass", "slopeFactor_mass");
    QString minPeakArea_mass = getPropertyValue("PeakFindingParametersMass", "minPeakArea_mass");
    QString minPeakHeight_mass = getPropertyValue("PeakFindingParametersMass", "minPeakHeight_mass");
    QString folderWidthOfNoise_mass = getPropertyValue("PeakFindingParametersMass", "folderWidthOfNoise_mass");

    qDebug() << "从XML读取的质谱寻峰参数原始值:";
    qDebug() << "  noiseWindow_mass:" << noiseWindow_mass;
    qDebug() << "  minPeakWidth_mass:" << minPeakWidth_mass;
    qDebug() << "  slopeFactor_mass:" << slopeFactor_mass;
    qDebug() << "  minPeakArea_mass:" << minPeakArea_mass;
    qDebug() << "  minPeakHeight_mass:" << minPeakHeight_mass;
    qDebug() << "  folderWidthOfNoise_mass:" << folderWidthOfNoise_mass;

    if (!noiseWindow_mass.isEmpty())
        params.noiseWindow_mass = noiseWindow_mass.toInt();
    if (!minPeakWidth_mass.isEmpty())
        params.minPeakWidth_mass = minPeakWidth_mass.toInt();
    if (!slopeFactor_mass.isEmpty())
        params.slopeFactor_mass = slopeFactor_mass.toDouble();
    if (!minPeakArea_mass.isEmpty())
        params.minPeakArea_mass = minPeakArea_mass.toDouble();
    if (!minPeakHeight_mass.isEmpty())
        params.minPeakHeight_mass = minPeakHeight_mass.toDouble();
    if (!folderWidthOfNoise_mass.isEmpty())
        params.folderWidthOfNoise_mass = folderWidthOfNoise_mass.toInt();

    qDebug() << "加载后的质谱参数值:";
    qDebug() << "  useDefault_mass:" << params.useDefault_mass;
    qDebug() << "  smoothType_mass:" << params.smoothType_mass;
    qDebug() << "  windowSize_mass:" << params.windowSize_mass;
    qDebug() << "  gaussianSigma_mass:" << params.gaussianSigma_mass;
    qDebug() << "  polyOrder_mass:" << params.polyOrder_mass;
    qDebug() << "  baselineType_mass:" << params.baselineType_mass;
    qDebug() << "  alsLambda_mass:" << params.alsLambda_mass;
    qDebug() << "  alsP_mass:" << params.alsP_mass;
    qDebug() << "  alsMaxIter_mass:" << params.alsMaxIter_mass;
    qDebug() << "  noiseWindow_mass:" << params.noiseWindow_mass;
    qDebug() << "  minPeakWidth_mass:" << params.minPeakWidth_mass;
    qDebug() << "  slopeFactor_mass:" << params.slopeFactor_mass;
    qDebug() << "  minPeakArea_mass:" << params.minPeakArea_mass;
    qDebug() << "  minPeakHeight_mass:" << params.minPeakHeight_mass;
    qDebug() << "  folderWidthOfNoise_mass:" << params.folderWidthOfNoise_mass;

    qDebug() << "OptionsDialogSettings::loadPeakFindingParametersMassFromXml: 质谱寻峰参数加载完成";

    return true;
}

// loadConfig方法已在前面定义，移除重复定义
