/* ========================================
   LxChart 子项目专用样式表
   ======================================== */

/* ========================================
   通用按钮样式
   ======================================== */
QPushButton {
  background-color: rgb(245, 245, 245);
  border: 1px solid #c0c0c0;
  border-radius: 4px;
  font-size: 14px;
  color: #333333;
}

QPushButton:hover {
  border-color: #adadad;
  border-width: 2px;
}

QPushButton:pressed {
  background-color: #d4d4d4;
  border-color: #8c8c8c;
}

QPushButton:disabled {
  background-color: #f5f5f5;
  border-color: #d9d9d9;
  color: #999999;
}

/* ========================================
   滚动条样式
   ======================================== */
/* QScrollBar:vertical {
    background-color: #f0f0f0;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #c0c0c0;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #a0a0a0;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    border: none;
    background: none;
}

QScrollBar:horizontal {
    background-color: #f0f0f0;
    height: 12px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal {
    background-color: #c0c0c0;
    border-radius: 6px;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #a0a0a0;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    border: none;
    background: none;
} */

/* ========================================
   工具提示样式
   ======================================== */
/* QToolTip {
  background-color: #2b2b2b;
  color: #ffffff;
  border: 1px solid #555555;
  font-size: 18px; 
  font-family: "Microsoft YaHei UI", "Segoe UI", Arial, sans-serif;
  font-weight: normal;
} */

/* ========================================
   图表容器样式
   ======================================== */
QWidget#LxChart {
  background-color: #ffffff;
  border: 2px solid #e0e0e0;
}

/* 图表视图区域 */
QGraphicsView {
  background-color: #ffffff;
  border: 2px solid #d0d0d0;
  border-radius: 5px;
}

/* ========================================
   状态栏和标签样式
   ======================================== */
QLabel {
  color: #333333;
  font-size: 12px;
}

QLabel[type="coordinate"] {
  background-color: rgba(0, 0, 0, 180);
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: "Consolas", "Monaco", monospace;
  font-size: 11px;
}

/* ========================================
   分组框样式
   ======================================== */
QGroupBox {
  font-weight: bold;
  border: 2px solid #d0d0d0;
  border-radius: 6px;
  margin-top: 10px;
  padding-top: 10px;
}

QGroupBox::title {
  subcontrol-origin: margin;
  left: 10px;
  padding: 0 8px 0 8px;
  color: #555555;
}

/* ========================================
   滑块样式 - 自定义图片设计
   ======================================== */
QSlider::groove:vertical {
  background: #e0e0e0;
  width: 6px;
  border-radius: 3px;
  border: 1px solid #d0d0d0;
}

QSlider::handle:vertical {
  /* 使用自定义图片 - 请将你的图片放到 Icons/LxChart/Slider/ 目录下 */
  image: url(:/Icons/LxChart/Slider/slider_normal.png);
  width: 20px;
  height: 20px;
  margin: 0 -9px; /* 让图片居中对齐滑槽 */
  border: none; /* 移除边框，让图片完全显示 */
}

QSlider::handle:vertical:hover {
  image: url(:/Icons/LxChart/Slider/slider_hover.png);
}

QSlider::handle:vertical:pressed {
  image: url(:/Icons/LxChart/Slider/slider_pressed.png);
}

/* 备用样式：如果图片不存在，使用纯色作为后备 */
QSlider::handle:vertical[fallback="true"] {
  background: #808080;
  border: 2px solid #505050;
  width: 18px;
  height: 18px;
  border-radius: 11px;
  margin: 0 -8px;
}

QSlider::handle:vertical:hover[fallback="true"] {
  background: #909090;
  border: 2px solid #404040;
}

QSlider::handle:vertical:pressed[fallback="true"] {
  background: #606060;
  border: 2px solid #303030;
}

QSlider::add-page:vertical {
  background: #e0e0e0;
  border-radius: 3px;
  border: 1px solid #d0d0d0;
}

QSlider::sub-page:vertical {
  background: #b0b0b0;
  border-radius: 3px;
  border: 1px solid #a0a0a0;
}

/* ========================================
   深色主题支持（可选）
   ======================================== */
QWidget[theme="dark"] {
  background-color: #2b2b2b;
  color: #ffffff;
}

QWidget[theme="dark"] QPushButton {
  background-color: #404040;
  border-color: #606060;
  color: #ffffff;
}

QWidget[theme="dark"] QPushButton:hover {
  background-color: #505050;
}
