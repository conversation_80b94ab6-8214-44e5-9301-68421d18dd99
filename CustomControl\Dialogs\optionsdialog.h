#ifndef OPTIONSDIALOG_H
#define OPTIONSDIALOG_H

#include <QDialog>
#include <QDebug>
#include <QRegularExpression>
#include <QRegularExpressionValidator>
#include <QMessageBox>
#include "Config/PeakFindingConfigs.h"

namespace Ui
{
    class OptionsDialog;
}

class OptionsDialog : public QDialog
{
    Q_OBJECT

public:
    explicit OptionsDialog(QWidget *parent = nullptr);
    ~OptionsDialog();

    // 获取当前寻峰配置
    PeakFindingParameters getPeakFindingParameters() const;

    // 获取当前外观配置
    AppearanceSettings getAppearanceSettings() const;

signals:
    void peakLabelTypeChanged(GlobalEnums::TrackType trackType);

public slots:
    // 重写show方法，确保每次显示时都重新加载配置
    void show();

private slots:
    void onUseDefaultChanged(bool useDefault);
    void onSmoothParamsChanged();
    void onBaselineParamsChanged();
    void onPeakFindParamsChanged();
    void onClearPeakStatusClicked(); // 保留清除寻峰状态功能

    // 质谱积分参数槽函数
    void onUseDefaultMassChanged(bool useDefault);
    void onSmoothMassParamsChanged();
    void onBaselineMassParamsChanged();
    void onPeakFindMassParamsChanged();

    // 外观页面按钮槽函数
    void onSetAxisLabelFontClicked();
    void onSetAutoLabelFontClicked();
    void onSetCustomLabelDefaultClicked();
    void onSetAxisTitleFontClicked();
    void onSetTitleFontClicked();
    void onLineWidthChanged(int value);
    void onOkClicked();
    void onResetAllOptionsClicked();

private:
    void connectAll();
    void initUiParams();
    void setupPeakFindingUI();
    void setupValidators();
    void updatePeakFindingUI();
    void updatePeakFindingMassUI();
    void updatePeakFindingConfig();
    void updatePeakFindingMassConfig();
    bool validatePeakFindingParams();
    void reloadConfigurations(); // 重新加载所有配置

private:
    Ui::OptionsDialog *ui;
    PeakFindingParameters m_initialPeakParams;         // 对话框打开时的初始寻峰参数
    PeakFindingParametersMass m_initialPeakParamsMass; // 对话框打开时的初始质谱寻峰参数
};

#endif // OPTIONSDIALOG_H
