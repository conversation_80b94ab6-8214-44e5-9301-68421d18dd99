#pragma once
#include <vector>
#include <Eigen/Dense>

/**
 * @brief 简单移动平均滤波
 * @details 计算指定窗口大小内数据点的平均值，用于平滑信号数据
 * @param data 输入的原始信号数据向量
 * @param windowSize 平均窗口大小，默认为5
 * @return 平滑后的信号数据向量
 */
std::vector<double> movingAverage(const std::vector<double>& data, size_t  windowSize = 5);

/**
 * @brief 生成高斯核
 * @details 创建用于高斯滤波的高斯核权重向量
 * @param size 核的大小（必须是奇数）
 * @param sigma 高斯函数的标准差，控制平滑程度
 * @return 归一化后的高斯核权重向量
 */
std::vector<double> gaussianKernel(int size, double sigma) ;

/**
 * @brief 高斯滤波平滑
 * @details 使用高斯核对信号进行卷积，实现平滑效果
 * @param data 输入的原始信号数据向量
 * @param kernelSize 高斯核大小，必须为奇数，默认为5
 * @param sigma 高斯函数标准差，控制平滑程度，默认为1.0
 * @return 高斯滤波平滑后的信号数据向量
 */
std::vector<double> gaussianSmooth(const std::vector<double>& data, int kernelSize = 5, double sigma = 1.0);

/**
 * @brief 多项式拟合系数计算
 * @details 使用最小二乘法计算多项式拟合的系数
 * @param x 自变量向量
 * @param y 因变量向量
 * @param degree 多项式阶数
 * @return 多项式系数向量（从常数项到高阶项）
 */
Eigen::VectorXd polyfit(const Eigen::VectorXd& x, const Eigen::VectorXd& y, int degree);

/**
 * @brief Savitzky-Golay滤波
 * @details 使用多项式局部拟合的方法平滑信号，能更好地保留峰值特征
 * @param data 输入的原始信号数据向量
 * @param windowSize 滤波窗口大小，必须为奇数，默认为5
 * @param polyOrder 多项式阶数，默认为2
 * @return Savitzky-Golay滤波平滑后的信号数据向量
 */
std::vector<double> savitzkyGolay(const std::vector<double>& data, int windowSize=5, int polyOrder=2);

/**
 * @brief 平滑算法使用示例函数
 * @details 展示如何调用各种平滑函数的示例代码
 * @return 0表示示例执行成功
 */
int example_of_invoke_smoothing();
