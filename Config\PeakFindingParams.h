#ifndef PEAKFINDINGPARAMS_H
#define PEAKFINDINGPARAMS_H

#include <QString>
#include <QSettings>
#include <QDebug>

/**
 * @brief 平滑算法类型枚举
 */
enum class SmoothType
{
    Average = 0,  // 移动平均
    Gaussian = 1, // 高斯平滑
    SGolay = 2    // <PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>滤波
};

/**
 * @brief 基线校正算法类型枚举
 */
enum class BaselineType
{
    Cubic = 0, // 三次多项式
    ALS = 1    // 非对称最小二乘
};

/**
 * @brief 平滑参数结构体
 */
struct SmoothParams
{
    SmoothType type = SmoothType::Average; // 平滑方法
    int windowSize = 5;                    // 窗口大小 (3,5,7,9,11)
    double gaussianSigma = 1.0;            // 高斯σ (0.5-3.0)
    int sgPolyOrder = 2;                   // S-G多项式阶数 (1-4)

    // 默认构造函数
    SmoothParams() = default;

    // 验证参数有效性
    bool isValid() const
    {
        return windowSize >= 3 && windowSize <= 11 && (windowSize % 2 == 1) &&
               gaussianSigma >= 0.5 && gaussianSigma <= 3.0 &&
               sgPolyOrder >= 1 && sgPolyOrder <= 4;
    }

    // 重置为默认值
    void reset()
    {
        type = SmoothType::Average;
        windowSize = 5;
        gaussianSigma = 1.0;
        sgPolyOrder = 2;
    }
};

/**
 * @brief 基线校正参数结构体
 */
struct BaselineParams
{
    BaselineType type = BaselineType::Cubic; // 基线校正方法
    double alsLambda = 1e5;                  // ALS λ参数 (1e3-1e7)
    double alsP = 0.01;                      // ALS p参数 (0.001-0.1)
    int alsMaxIter = 10;                     // ALS迭代次数 (5-20)

    // 默认构造函数
    BaselineParams() = default;

    // 验证参数有效性
    bool isValid() const
    {
        return alsLambda >= 1e3 && alsLambda <= 1e7 &&
               alsP >= 0.001 && alsP <= 0.1 &&
               alsMaxIter >= 5 && alsMaxIter <= 20;
    }

    // 重置为默认值
    void reset()
    {
        type = BaselineType::Cubic;
        alsLambda = 1e5;
        alsP = 0.01;
        alsMaxIter = 10;
    }
};

/**
 * @brief 寻峰参数结构体
 */
struct PeakFindParams
{
    int noiseWindow = 20;        // 噪声窗口 (10-50)
    int minPeakWidth = 5;        // 最小峰宽 (2-20)
    double slopeFactor = 2.0;    // 斜率因子 (1.0-5.0)
    double minPeakArea = 0.0;    // 最小峰面积 (0=自动)
    double minPeakHeight = 0.0;  // 最小峰高 (0=自动)
    int folderWidthOfNoise = 10; // SNR噪声窗口倍数 (1-50)

    // 默认构造函数
    PeakFindParams() = default;

    // 验证参数有效性
    bool isValid() const
    {
        return noiseWindow >= 10 && noiseWindow <= 50 &&
               minPeakWidth >= 2 && minPeakWidth <= 20 &&
               slopeFactor >= 1.0 && slopeFactor <= 5.0 &&
               minPeakArea >= 0.0 && minPeakHeight >= 0.0 &&
               folderWidthOfNoise >= 1 && folderWidthOfNoise <= 50;
    }

    // 重置为默认值
    void reset()
    {
        noiseWindow = 20;
        minPeakWidth = 5;
        slopeFactor = 2.0;
        minPeakArea = 0.0;
        minPeakHeight = 0.0;
        folderWidthOfNoise = 10;
    }
};

/**
 * @brief 完整的寻峰参数配置结构体
 */
struct PeakFindingConfig
{
    bool useDefault = true;        // 是否使用默认参数
    SmoothParams smoothParams;     // 平滑参数
    BaselineParams baselineParams; // 基线校正参数
    PeakFindParams peakFindParams; // 寻峰参数

    // 默认构造函数
    PeakFindingConfig() = default;

    // 验证所有参数有效性
    bool isValid() const
    {
        return smoothParams.isValid() &&
               baselineParams.isValid() &&
               peakFindParams.isValid();
    }

    // 重置所有参数为默认值
    void reset()
    {
        useDefault = true;
        smoothParams.reset();
        baselineParams.reset();
        peakFindParams.reset();
    }

    // 保存到QSettings
    void saveToSettings(QSettings &settings) const
    {
        settings.beginGroup("PeakFinding");

        settings.setValue("useDefault", useDefault);

        // 平滑参数
        settings.beginGroup("Smooth");
        settings.setValue("type", static_cast<int>(smoothParams.type));
        settings.setValue("windowSize", smoothParams.windowSize);
        settings.setValue("gaussianSigma", smoothParams.gaussianSigma);
        settings.setValue("sgPolyOrder", smoothParams.sgPolyOrder);
        settings.endGroup();

        // 基线校正参数
        settings.beginGroup("Baseline");
        settings.setValue("type", static_cast<int>(baselineParams.type));
        settings.setValue("alsLambda", baselineParams.alsLambda);
        settings.setValue("alsP", baselineParams.alsP);
        settings.setValue("alsMaxIter", baselineParams.alsMaxIter);
        settings.endGroup();

        // 寻峰参数
        settings.beginGroup("PeakFind");
        settings.setValue("noiseWindow", peakFindParams.noiseWindow);
        settings.setValue("minPeakWidth", peakFindParams.minPeakWidth);
        settings.setValue("slopeFactor", peakFindParams.slopeFactor);
        settings.setValue("minPeakArea", peakFindParams.minPeakArea);
        settings.setValue("minPeakHeight", peakFindParams.minPeakHeight);
        settings.setValue("folderWidthOfNoise", peakFindParams.folderWidthOfNoise); // 添加SNR参数保存
        settings.endGroup();

        settings.endGroup();
    }

    // 从QSettings加载
    void loadFromSettings(QSettings &settings)
    {
        settings.beginGroup("PeakFinding");

        useDefault = settings.value("useDefault", true).toBool();

        // 平滑参数
        settings.beginGroup("Smooth");
        smoothParams.type = static_cast<SmoothType>(settings.value("type", 0).toInt());
        smoothParams.windowSize = settings.value("windowSize", 5).toInt();
        smoothParams.gaussianSigma = settings.value("gaussianSigma", 1.0).toDouble();
        smoothParams.sgPolyOrder = settings.value("sgPolyOrder", 2).toInt();
        settings.endGroup();

        // 基线校正参数
        settings.beginGroup("Baseline");
        baselineParams.type = static_cast<BaselineType>(settings.value("type", 0).toInt());
        baselineParams.alsLambda = settings.value("alsLambda", 1e5).toDouble();
        baselineParams.alsP = settings.value("alsP", 0.01).toDouble();
        baselineParams.alsMaxIter = settings.value("alsMaxIter", 10).toInt();
        settings.endGroup();

        // 寻峰参数
        settings.beginGroup("PeakFind");
        peakFindParams.noiseWindow = settings.value("noiseWindow", 20).toInt();
        peakFindParams.minPeakWidth = settings.value("minPeakWidth", 5).toInt();
        peakFindParams.slopeFactor = settings.value("slopeFactor", 2.0).toDouble();
        peakFindParams.minPeakArea = settings.value("minPeakArea", 0.0).toDouble();
        peakFindParams.minPeakHeight = settings.value("minPeakHeight", 0.0).toDouble();
        peakFindParams.folderWidthOfNoise = settings.value("folderWidthOfNoise", 10).toInt(); // 添加SNR参数加载
        settings.endGroup();

        settings.endGroup();
    }
};

/**
 * @brief 全局寻峰配置管理器
 */
class PeakFindingConfigManager
{
public:
    static PeakFindingConfigManager &instance()
    {
        static PeakFindingConfigManager instance;
        return instance;
    }

    // 获取当前配置
    const PeakFindingConfig &getConfig() const { return m_config; }

    // 设置配置
    void setConfig(const PeakFindingConfig &config)
    {
        m_config = config;
        saveConfig();
    }

    // 保存配置到文件
    void saveConfig()
    {
        QSettings settings("HZHDataReader", "PeakFinding");
        m_config.saveToSettings(settings);
        qDebug() << "寻峰配置已保存";
    }

    // 从文件加载配置
    void loadConfig()
    {
        QSettings settings("HZHDataReader", "PeakFinding");
        m_config.loadFromSettings(settings);
        qDebug() << "寻峰配置已加载";
    }

    // 重置为默认配置
    void resetConfig()
    {
        m_config.reset();
        saveConfig();
        qDebug() << "寻峰配置已重置为默认值";
    }

private:
    PeakFindingConfig m_config;

    PeakFindingConfigManager()
    {
        loadConfig();
    }
};

#endif // PEAKFINDINGPARAMS_H
