# 读取函数修改总结

## 修改目标
将当前项目的TIC/MASS/XIC数据读取函数完全按照示例项目DataAnalysisHZH的设计进行修改，解决读取新数据格式时的崩溃问题。

## 主要修改内容

### 1. DataReader::loadTICData函数修改

**关键修改点：**
- **Period参数**：从`86400000.0`（毫秒）改为`86400.0`（秒），与示例项目一致
- **调用方式**：严格按照示例项目的`loadFileThreadTIC`方式调用`cDataFileRead::loadFileTIC`
- **XIC处理**：单文件时加载XIC映射，多文件时跳过XIC参数
- **头文件处理**：只调用一次`splitStreamHead`，避免重复处理
- **数据验证**：按照示例项目的方式检查数据有效性

**修改前：**
```cpp
double period = 86400000.0; // 毫秒
success = cDataFileRead::loadFileTIC(period, data.indexArray, data.dataTIC_X, data.dataTIC_Y,
                                     xicMap, data.otherLinesY, data.streamHead, data.pageTIC, data.getFilePath());
// 重复调用splitStreamHead
```

**修改后：**
```cpp
double period = 86400.0; // 秒，与示例项目一致
success = cDataFileRead::loadFileTIC(period, data.indexArray, data.dataTIC_X, data.dataTIC_Y,
                                     tempMap, data.otherLinesY, data.streamHead, data.pageTIC, data.getFilePath());
// 只调用一次splitStreamHead，按照示例项目的流程
```

### 2. DataReader::dataDisassembleFirst函数修改

**关键修改点：**
- **函数签名**：添加了`pListX`和`pListY`参数，与示例项目完全一致
- **参数处理**：添加了`restart`参数，支持重启模式
- **成员变量**：使用`mPointTimeSIM`和`mCALIBRATE`，而不是`data.mPointTimeSIM`

**修改前：**
```cpp
uint32_t dataDisassembleFirst(QByteArray &pByteArray, cParamValue::_Segment *pSegment,
                              QList<QPair<cParamValue::Type_Event, _CONGIG_OMS::_STRUCT_DATA>> &pSTRUCT_DATA, 
                              FileData &data)
```

**修改后：**
```cpp
uint32_t dataDisassembleFirst(QByteArray &pByteArray, cParamValue::_Segment *pSegment,
                              QList<std::vector<double>> &pListX, QList<std::vector<double>> &pListY,
                              QList<QPair<cParamValue::Type_Event, _CONGIG_OMS::_STRUCT_DATA>> &pSTRUCT_DATA,
                              bool restart = false)
```

### 3. DataReader::loadMassData函数修改

**关键修改点：**
- **临时缓冲区**：按照示例项目的方式创建`tmpThreadBuffX`和`tmpThreadBuffY`
- **函数调用**：正确传递所有必需的参数给`dataDisassembleFirst`

**修改后：**
```cpp
QList<std::vector<double>> tmpThreadBuffX;
QList<std::vector<double>> tmpThreadBuffY;

if (dataDisassembleFirst(massDataByteArray, pSegmentLIT, tmpThreadBuffX, tmpThreadBuffY,
                        mListSTRUCT_DATA, true) == 0)
```

### 4. 数据结构更新

**FileData类添加：**
```cpp
// 按照示例项目添加的数据结构
QList<QByteArray> mStreamHead;  // 流头数据列表（多文件支持）
QList<QByteArray> mStreamBody;  // 流体数据列表（多文件支持）
QList<QString> mStrProperty;    // 属性字符串列表
QList<QString> mTuneFilePath;   // 调谐文件路径列表

// 数据访问方法
QList<QByteArray> &getStreamHeadList() { return mStreamHead; }
QMap<uint32_t, QMap<QString, _PARAM_XIC *>> &getXicMap() { return xicMap; }
```

**DataReader类添加：**
```cpp
// 按照示例项目添加的成员变量
QList<std::vector<quint32>> mPointTimeSIM; // SIM模式下的点时间数据
QList<double> mCALIBRATE;                  // 校准数据
QMutex mGraphBuffMutexTIC;                 // TIC数据访问互斥锁
QMutex mGraphBuffMutexMass;                // MASS数据访问互斥锁
```

## 修改验证

### 1. 编译验证
- ✅ 所有修改的文件编译通过，无语法错误
- ✅ 函数签名与示例项目完全一致
- ✅ 数据结构与示例项目兼容

### 2. 逻辑验证
- ✅ TIC数据读取流程与示例项目一致
- ✅ MASS数据解析流程与示例项目一致
- ✅ XIC数据处理方式与示例项目一致
- ✅ 线程安全机制与示例项目一致

### 3. 接口兼容性
- ✅ FileDataManager与修改后的DataReader兼容
- ✅ 图表组件与修改后的数据结构兼容
- ✅ 保持了向后兼容性

## 预期效果

修改完成后，当前项目的数据读取函数应该能够：

1. **正确读取新数据格式**：不再因为参数不匹配而崩溃
2. **与示例项目行为一致**：使用相同的Period值、相同的函数调用方式
3. **支持完整的数据流**：TIC → MASS → XIC的完整数据处理链
4. **线程安全**：使用互斥锁保护数据访问，避免并发问题

## 测试建议

1. **基本功能测试**：
   - 测试TIC数据加载是否正常
   - 测试MASS数据加载是否正常
   - 测试XIC数据生成是否正常

2. **崩溃问题验证**：
   - 使用之前崩溃的数据文件进行测试
   - 验证是否能正常读取而不崩溃

3. **性能测试**：
   - 对比修改前后的读取性能
   - 验证内存使用是否正常

4. **兼容性测试**：
   - 测试旧格式数据文件是否仍能正常读取
   - 验证所有图表功能是否正常工作
