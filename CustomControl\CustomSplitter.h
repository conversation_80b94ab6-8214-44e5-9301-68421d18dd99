#ifndef CUSTOMSPLITTER_H
#define CUSTOMSPLITTER_H

#include <QSplitter>
#include <QPainter>
#include <QSplitterHandle>
#include <QStyleOption>
#include <QPaintEvent>

// 自定义分隔器句柄类
class CustomSplitterHandle : public QSplitterHandle
{
    Q_OBJECT
public:
    explicit CustomSplitterHandle(Qt::Orientation orientation, QSplitter *parent = nullptr);

    // 设置背景颜色
    void setColor(const QColor &color);
    // 设置分隔线宽度
    void setLineWidth(int width);
    // 设置背景图片
    void setImage(const QPixmap &pixmap);
    // 设置虚线样式
    void setDashed(bool dashed);
    // 设置虚线样式参数
    void setDashPattern(const QVector<qreal> &pattern);

protected:
    void paintEvent(QPaintEvent *event) override;

private:
    QColor m_color = Qt::gray;               // 默认颜色为灰色
    int m_lineWidth = 1;                     // 默认线宽为1像素
    QPixmap m_pixmap;                        // 背景图片
    bool m_dashed = false;                   // 是否使用虚线
    QVector<qreal> m_dashPattern = { 5, 5 }; // 默认虚线模式：5像素画线，5像素空白
};

// 自定义分隔器类
class CustomSplitter : public QSplitter
{
    Q_OBJECT
public:
    explicit CustomSplitter(QWidget *parent = nullptr);
    explicit CustomSplitter(Qt::Orientation orientation, QWidget *parent = nullptr);

    // 设置分隔线颜色
    void setHandleColor(const QColor &color);
    // 设置分隔线宽度
    void setHandleWidth(int width);
    // 设置分隔线背景图片
    void setHandleImage(const QPixmap &pixmap);
    // 设置分隔线为虚线
    void setHandleDashed(bool dashed);
    // 设置虚线样式参数
    void setHandleDashPattern(const QVector<qreal> &pattern);

protected:
    QSplitterHandle *createHandle() override;

private:
    QColor m_handleColor = Qt::gray;
    int m_handleLineWidth = 1;
    QPixmap m_handlePixmap;
    bool m_handleDashed = false;
    QVector<qreal> m_handleDashPattern = { 5, 5 };
};

#endif // CUSTOMSPLITTER_H
