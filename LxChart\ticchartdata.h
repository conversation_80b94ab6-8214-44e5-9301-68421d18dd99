#ifndef TICCHARTDATA_H
#define TICCHARTDATA_H

#include "lxchartdata.h"
#include <QMap>
#include <QUuid>
#include <QReadWriteLock>
#include <QMutex>
#include <memory>
#include <atomic>

// 前向声明
class MassChartData;
class XicChartData;

/**
 * @brief TicChartData类用于给LxChart控件提供TIC数据
 * 管理一个MASS和多个XIC数据对象，建立清晰的层级关系
 */
class TicChartData : public LxChartData
{
    Q_OBJECT
public:
    /**
     * @brief 构造函数
     * @param ParamPath 从属的FileData对象Param文件绝对路径
     * @param eventId 事件ID
     * @param ionMode 离子模式
     * @param scanMode 扫描模式
     * @param dataProcess 数据处理方式
     * @param dataName 数据名
     * @param sampleName 样本名
     * @param parent 父对象
     */
    explicit TicChartData(QString ParamPath, int eventId, GlobalEnums::IonMode ionMode = GlobalEnums::IonMode::NagativeIon,
                          GlobalEnums::ScanMode scanMode = GlobalEnums::ScanMode::MRM, QString dataProcess = "无处理", QString dataName = "默认数据名",
                          QString sampleName = "默认样本名", QObject *parent = nullptr);

    /**
     * @brief 析构函数 - 确保正确清理MASS和XIC数据
     */
    ~TicChartData();

    // MASS数据管理 - 线程安全版本
    MassChartData *getMassData() const { return m_massData; } // 保持兼容性
    bool hasMassData() const;                                 // 线程安全检查
    std::shared_ptr<MassChartData> getMassDataPtr() const;    // 获取智能指针
    MassChartData *createMassDataSafe();                      // 线程安全创建MASS数据
    void deleteMassDataSafe();                                // 线程安全删除MASS数据
    bool isMassDataLoading() const { return m_massDataLoading.load(); }
    bool isMassDataReady() const { return m_massDataReady.load(); }

    // 兼容性方法（已废弃，建议使用Safe版本）
    MassChartData *createMassData(); // 创建MASS数据对象
    void deleteMassData();           // 删除MASS数据对象

    // XIC数据管理（一对多）
    QVector<XicChartData *> getXicDataList() const { return m_xicDataVector; } // 按创建顺序返回XIC列表
    bool hasXicData() const { return !m_xicDataVector.isEmpty(); }
    XicChartData *getXicData(const QUuid &xicId) const; // 根据ID获取XIC数据
    XicChartData *getLatestXicData() const;             // 获取最新创建的XIC数据
    QUuid createXicData();                              // 创建XIC数据对象，返回新创建的XIC的UUID
    bool deleteXicData(const QUuid &xicId);             // 删除指定ID的XIC数据对象
    void deleteAllXicData();                            // 删除所有XIC数据对象

    // 事件ID访问
    int getEventId() const { return m_eventId; }

    // 移动浏览功能相关
    int getLastXDataIndex() const { return m_lastXDataIndex; }
    void setLastXDataIndex(int index) { m_lastXDataIndex = index; }
    bool hasValidLastXDataIndex() const { return m_lastXDataIndex >= 0; }

signals:
    // 🎯 数据加载完成信号，用于UI组件延迟加载
    void massDataLoadCompleted(int eventId, MassChartData *massData);
    void massDataLoadFailed(int eventId, const QString &errorMessage);
    void massDataAddedToChart(int eventId); // MASS数据已添加到图表

private:
    int m_eventId; // 事件ID

    // 🎯 智能指针管理MASS数据，避免悬空指针
    std::shared_ptr<MassChartData> m_massDataPtr; // 智能指针管理的MASS数据
    MassChartData *m_massData = nullptr;          // 保持兼容性的原始指针

    // 🎯 线程安全保护
    mutable QReadWriteLock m_massDataLock; // MASS数据读写锁
    mutable QMutex m_xicDataMutex;         // XIC数据互斥锁

    // 🎯 加载状态管理
    std::atomic<bool> m_massDataLoading{false}; // MASS数据加载状态
    std::atomic<bool> m_massDataReady{false};   // MASS数据就绪状态

    QMap<QUuid, XicChartData *> m_xicDataMap; // XIC数据映射，用于根据ID快速查找
    QVector<XicChartData *> m_xicDataVector;  // XIC数据有序列表，保持创建顺序
    QList<QUuid> m_xicCreationOrder;          // XIC创建顺序列表
    int m_lastXDataIndex = -1;                // 上次双击时X轴数值对应的X数组索引，用于移动浏览
};

#endif // TICCHARTDATA_H
